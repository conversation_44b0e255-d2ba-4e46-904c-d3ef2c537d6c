import Route from '@ioc:Adonis/Core/Route';
import NatService from 'App/Services/NatService';

Route.group(() => {
  Route.group(() => {
    Route.get('/test-subscription', async (ctx) => {
      const nats = await NatService;
      const data = {
        insurance_year_id: 1,
        package_id: 1,
        team_id: 1,
      };
      const coverageResponse = await nats.request(
        'team.subscription.created',
          data,
        30000
      );
      nats.subscribe('coverage.team_saved', (message) => {
        console.log('Message received: ', message);
      });
      return ctx.response.json(coverageResponse);
    });
  }).prefix('test');
}).prefix('api')
