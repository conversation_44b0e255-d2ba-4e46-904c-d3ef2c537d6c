import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompanyAgency from './InsuranceCompanyAgency'
import Package from './Package'
import Country from './Country'
import City from './City'
import InsuranceCompanyHospital from './InsuranceCompanyHospital'
import { ManyToMany } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompanyPharmacy from './InsuranceCompanyPharmacy'
import InsuranceCompanyLaboratory from './InsuranceCompanyLaboratory'

export default class InsuranceCompany extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'uuid' })
  public uuid: string | null

  @column()
  public name: string

  @column()
  public agrement: string | null

  @column()
  public email: string | null

  @column()
  public phone: string

  @column()
  public address: string | null

  @column()
  public seat: string | null

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number | null

  @column()
  public website: string | null

  @column()
  public logo: string | null

  @column({ columnName: 'probation_duration' })
  public probationDuration: number | null

  @column()
  public status: string = 'active'

  @column()
  public type: 'company' | 'mutual' = 'company'

  @column()
  public description: string | null

  @column()
  public configs: any | null

  @column()
  public subscription_model: 'certificats' | 'plafond' | null

  @column()
  public certificat: {
    is_required?: boolean,
    is_limit?: boolean,
    total_used_month?: number,
    total_used_year?: number,
    validity?: string
  } | null

  @column()
  public notes: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime | null

  @column({ columnName: 'active_refund' })
  public activeRefund: boolean = false

  @hasMany(() => InsuranceCompanyAgency, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id'
  })
  public agencies: HasMany<typeof InsuranceCompanyAgency>

  @hasMany(() => Package, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id'
  })
  public packages: HasMany<typeof Package>

  @belongsTo(() => Country, {
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @manyToMany(() => InsuranceCompanyHospital, {
    pivotTable: 'insurance_company_hospitals',
    pivotColumns: ['insurance_company_id', 'health_institute_id', 'is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'healthInstituteId',
    pivotTimestamps: true,
  })
  public hospitals: ManyToMany<typeof InsuranceCompanyHospital>

  @manyToMany(() => InsuranceCompanyPharmacy, {
    pivotTable: 'insurance_company_pharmacies',
    pivotColumns: ['insurance_company_id', 'pharmacy_id', 'is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'pharmacyId',
    pivotTimestamps: true,
  })
  public pharmacies: ManyToMany<typeof InsuranceCompanyPharmacy>

  @manyToMany(() => InsuranceCompanyLaboratory, {
    pivotTable: 'insurance_company_laboratories',
    pivotColumns: ['insurance_company_id', 'laboratory_id', 'is_active'],
    pivotForeignKey: 'insuranceCompanyId',
    pivotRelatedForeignKey: 'laboratoryId',
    pivotTimestamps: true,
  })
  public laboratories: ManyToMany<typeof InsuranceCompanyLaboratory>
}
