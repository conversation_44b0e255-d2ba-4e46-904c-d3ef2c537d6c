import Env  from '@ioc:Adonis/Core/Env';
import * as Minio from 'minio';

class Storage {
  private client: Minio.Client;
  private accessKey = Env.get('MINIO_ACCESS_KEY');
  private secretKey = Env.get('MINIO_SECRET_KEY');
  private endPoint = Env.get('MINIO_ENDPOINT', 'minio');
  private port = parseInt(Env.get('MINIO_PORT', '9000'));
  private useSSL = Env.get('MINIO_USE_SSL', 'false') === 'true';

  constructor() {
    this.client = new Minio.Client({
      endPoint: this.endPoint,
      port: this.port,
      useSSL: this.useSSL,
      accessKey: this.accessKey,
      secretKey: this.secretKey,
      region: Env.get('MINIO_REGION', 'us-east-1')
    });
  }

  public generatePermanentUrl(bucketName: string, objectName: string): string {
    const protocol = this.useSSL ? 'https' : 'http';
    let hostname = !this.useSSL ? 'localhost' : 'minio';
    return `${protocol}://${hostname}:${this.port}/${bucketName}/${objectName}`;
  }

  public async uploadFile(bucketName: string, objectName: string, filePath: string, metadata?: any) {
    try {
      console.log("data", bucketName, objectName, filePath, metadata);

      const exists = await this.client.bucketExists(bucketName);
      if (!exists) {
        await this.client.makeBucket(bucketName, "us-east-1");
        console.log("Bucket created", bucketName);
      }
      // let objectName = `archives/${fileName}`;
      await this.client.fPutObject(bucketName, objectName, filePath, metadata);
      // console.log("File uploaded", objectName);

      return objectName;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  public async downloadFile(bucketName: string, fileName: string) {
    try {
      // Générer une URL pré-signée pour le fichier
      const fileUrl = await this.client.presignedUrl('GET', bucketName, fileName, 24 * 60 * 60); // URL valide pendant 24 heures
      console.log("File URL", fileUrl);
      return fileUrl;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}

export default Storage;
