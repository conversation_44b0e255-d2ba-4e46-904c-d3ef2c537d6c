import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator';
import { DateTime } from 'luxon';
import Database from '@ioc:Adonis/Lucid/Database';
// import { string } from '@ioc:Adonis/Core/Helpers';
import { cuid } from '@ioc:Adonis/Core/Helpers';
import { ApiResponse, NewTeamSubscriptionData, UserStatus } from 'App/Controllers/interfaces'
import HelperController from '../../helpers/HelperController';
import Team from 'App/Models/Team';
import User from 'App/Models/User';
import Patient from 'App/Models/Patient';
import Package from 'App/Models/Package';
import InsuranceContract from 'App/Models/InsuranceContract';
import Storage from 'App/Services/Storage';
import NatService from 'App/Services/NatService';

export default class TeamController extends HelperController {

  public async index({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const contracts = await InsuranceContract.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('team').paginate(page, limit);
      if (!contracts) {
        apiResponse.message = "Aucun groupe trouvé";
        return response.status(200).json(apiResponse);
      }
      // const meta = contracts.serialize().meta;
      const teams = contracts.map((contract) => {
        return contract.team;
      });
      apiResponse.success = true;
      apiResponse.message = 'Liste des équipes';
      apiResponse.result = teams;
      return response.status(status).json(apiResponse);
    } catch (error) {
      apiResponse.message = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getTeamDetails({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 200;
    try {
      const teamId = request.input('team_id');
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const team = await Team.query().where('id', teamId).where('insurance_company_id', insurance.id).first();
      if (!team) {
        apiResponse.message = "Groupe introuvable";
        return response.status(404).json(apiResponse);
      }
      apiResponse.success = true;
      apiResponse.message = "Détails du groupe récupérés avec succès";
      apiResponse.result = team;
      return response.status(status).json(apiResponse);
    } catch (error) {
      apiResponse.message = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getTeamMembers({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const teamId = request.input('team_id');
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const team = await Team.query().where('id', teamId).where('insurance_company_id', insurance.id).first();
      if (!team) {
        apiResponse.message = "Groupe introuvable";
        return response.status(404).json(apiResponse);
      }
      const members = await team.related('members').query().preload('patient').paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Membres du groupe récupérés avec succès";
      apiResponse.result = members;
      return response.status(status).json(apiResponse);
    } catch (error) {
      apiResponse.message = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async addTeam({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          legalName: schema.string.optional(),
          registrationNumber: schema.string.optional(),
          type: schema.enum(['company', 'association', 'collective', 'other']),
          industry: schema.string.optional(),
          employeeCount: schema.number.optional(),
          contactEmail: schema.string(),
          contactPhone: schema.string(),
          address: schema.string.optional(),
          postalCode: schema.string.optional(),
          cityId: schema.number.optional(),
          countryId: schema.number.optional(),
          responsable: schema.object().members({
            first_name: schema.string(),
            last_name: schema.string(),
            email: schema.string(),
            phone: schema.string(),
            gender: schema.enum(['M', 'F']),
            birthday_year: schema.number(),
            birthday_month: schema.number(),
            birthday_day: schema.number(),
            role: schema.enum(['admin', 'hr_manager']),
          }),
        }),
      });
      const {
        name, legalName, registrationNumber, type, industry, employeeCount, contactEmail, contactPhone, address, postalCode, cityId, countryId, responsable,
      } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const checkExist = await Team.query().where('name', name).orWhere('legal_name', String(legalName)).first();
      if (checkExist) {
        apiResponse.message = "Ce nom ou ce numero de registre est déjà utilisé";
        return response.status(400).json(apiResponse);
      }

      const activeYear = await this.getActiveYear(insurance.id);
      if (!activeYear) {
        apiResponse.message = "Aucune année assurantielle active n'est trouvée";
        return response.status(404).json(apiResponse);
      }



      const trx = await Database.transaction();
      try {
        let newUser: User = {} as User;
        let newPatient: Patient = {} as Patient;
        let publicId = cuid();
        let teamCode = name.substring(0, 3).toUpperCase() + Math.random().toString(36).substring(2, 6).toUpperCase();
        const newTeam = await Team.create({
          publicId: publicId,
          teamCode: teamCode,
          name: name,
          legalName: String(legalName),
          registrationNumber: String(registrationNumber),
          type: type as 'company' | 'association' | 'collective' | 'other',
          industry: String(industry),
          employeeCount: Number(employeeCount),
          contactEmail: String(contactEmail),
          contactPhone: String(contactPhone),
          address: String(address),
          postalCode: String(postalCode),
          cityId: Number(cityId),
          countryId: Number(countryId),
        }, { client: trx });

        if (!newTeam) {
          await trx.rollback();
          apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        const checkcontract = await InsuranceContract.query().where('team_id', newTeam.id).where('insurance_year_id', activeYear.id).first();
        if (checkcontract) {
          apiResponse.message = "Ce groupe a déjà un contrat pour cette année assurantielle";
          return response.status(400).json(apiResponse);
        }

        if (responsable) {
          const checkUser = await User.query().where('email', responsable.email).orWhere('phone', responsable.phone).where('role_id', 6).first();

          if (!checkUser) {
            let codeP = await this.generateCodeParrainage(8);
            const parrainage = {
              create_account: 0,
              active_qrcode: 0,
              adhesion_fees: 0,
              plan: 1,
              activeMoney: false
            }
            let username = responsable.first_name + responsable.last_name;
            newUser = await User.create({
              username: username,
              email: responsable.email,
              phone: responsable.phone,
              password: responsable.phone,
              countryId: countryId,
              languageId: 1,
              roleId: 6,
              creatorId: authUser.id,
              status: UserStatus.Actived,
              activatedAt: DateTime.now(),
              codeParrainage: codeP,
              parrainage: JSON.stringify(parrainage),
            }, { client: trx });

            if (!newUser) {
              await trx.rollback();
              apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
              status = 500;
              return response.status(status).json(apiResponse);
            }
            let codePatient = await this.generateToken();
            newPatient = await Patient.create({
              first_name: responsable.first_name,
              last_name: responsable.last_name,
              gender: responsable.gender,
              birthday_year: responsable.birthday_year,
              birthday_month: responsable.birthday_month,
              birthday_day: responsable.birthday_day,
              user_id: newUser.id,
              status: 'activated',
              code: codePatient,
              country_id: countryId,
            }, { client: trx });
            if (!newPatient) {
              await trx.rollback();
              apiResponse.message = "Echec de création du compte du responsable , une erreur serveur s'est produite";
              status = 500;
              return response.status(status).json(apiResponse);
            }
          }

          const newTeamMember = await newTeam.related('members').create({
            patientId: newPatient.id,
            publicId: cuid(),
            role: responsable.role as 'admin' | 'hr_manager' | 'member',
            createBySelf: false,
            joinedAt: DateTime.now(),
          }, { client: trx });
          if (!newTeamMember) {
            await trx.rollback();
            apiResponse.except = newTeamMember;
            apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        const newContract = await InsuranceContract.create({
          publicId: cuid(),
          insuranceYearId: activeYear.id,
          insuranceCompanyId: insurance.id,
          teamId: newTeam.id,
          isPaid: false,
          paymentDate: null,
          paymentReference: null,
          annualPremium: 0,
          paymentStatus: 'unpaid',
          documents: null,
          createdBy: authUser.id,
        }, { client: trx });

        if (!newContract) {
          await trx.rollback();
          apiResponse.message = "Echec de création du contrat, une erreur serveur s'est produite";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Groupe créé avec succès";
        apiResponse.result = {
          team: newTeam,
          responsable: newPatient,
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error in add new team", error);

        apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }

    } catch (error) {
      console.log("error in add new team", error);
      apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async createInsuranceContract({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          teamId: schema.number(),
          packageId: schema.number(),
          insuranceContractNumber: schema.string(),
          effectiveFrom: schema.date.optional(),
          effectiveTo: schema.date.optional(),
          isPaid: schema.boolean.optional(),
          paymentDate: schema.date.optional(),
          paymentReference: schema.string.optional(),
          annualPremium: schema.number.optional(),
          paymentStatus: schema.enum(['unpaid', 'partial', 'paid']),
          documents: schema.array.optional().members(
            schema.file({
              size: '2mb',
              extnames: ['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
            })
          )
        })
      });
      const {
        teamId, insuranceContractNumber, packageId, effectiveFrom, effectiveTo, isPaid, paymentDate, paymentReference, documents,
        annualPremium, paymentStatus,
      } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas autorisé à effectuer cette action";
        return response.status(401).json(apiResponse);
      }

      const team = await Team.findOrFail(teamId);
      if (!team) {
        apiResponse.message = "Ce groupe n'existe pas";
        return response.status(404).json(apiResponse);
      }
      const insurancePackage = await Package.query().where('id', packageId).where('type', 'team').first();
      if (!insurancePackage) {
        apiResponse.message = "Ce package n'existe pas";
        return response.status(404).json(apiResponse);
      }

      const activeYear = await this.getActiveYear(insurance.id);
      if (!activeYear) {
        apiResponse.message = "Aucune année assurantielle active n'est trouvée";
        return response.status(404).json(apiResponse);
      }

      const checkcontract = await InsuranceContract.query().where('team_id', teamId).where('insurance_year_id', activeYear.id).first();
      if (checkcontract) {
        apiResponse.message = "Ce groupe a déjà un contrat pour cette année assurantielle";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {

        let contractDocuments: any[] = [];
        if (documents && documents.length > 0) {
          const storage = new Storage();
          await Promise.all(documents.map(async (doc) => {
            const fileName = new Date().getTime() + "_" + doc.clientName;
            const objectName = `/contracts/${fileName}`;
            const filePath = String(doc.tmpPath);

            let bucketName = 'insurance';
            let metadata = {
              'Content-Type': doc.type,
              'Content-Disposition': `attachment; filename=${fileName}`,
              size: doc.size,
              extension: doc.extname,
            }

            // Upload file to storage
            await storage.uploadFile(bucketName, objectName, filePath, metadata);

            // Add document info to array
            let fileId = await this.generateUUID();
            contractDocuments.push({
              id: fileId,
              name: fileName,
              type: doc.type,
              size: doc.size,
              extension: doc.extname,
              url: objectName,
              uploadedAt: new Date()
            });
          }));
        }

        const newContract = await InsuranceContract.create({
          publicId: cuid(),
          contractNumber: insuranceContractNumber,
          insuranceYearId: activeYear.id,
          insuranceCompanyId: insurance.id,
          teamId: teamId,
          packageId: packageId,
          effectiveFrom: effectiveFrom,
          effectiveTo: effectiveTo,
          isPaid: isPaid,
          paymentDate: paymentDate,
          paymentReference: paymentReference,
          annualPremium: annualPremium,
          status: 'validate',
          paymentStatus: paymentStatus as 'unpaid' | 'partial' | 'paid',
          documents: JSON.stringify(contractDocuments),
          createdBy: authUser.id,
        }, { client: trx });
        if (!newContract) {
          await trx.rollback();
          apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Contrat créé avec succès";
        apiResponse.result = newContract;
        return response.status(status).json(apiResponse);
      } catch (error) {
        console.log("error in add new team", error);
        await trx.rollback();
        apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }

    } catch (error) {
      console.log("error in add new team", error);
      apiResponse.message = "Echec de création du groupe, une erreur serveur s'est produite";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async validateInsuranceContract({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          contractId: schema.number(),
          status: schema.enum(['validate', 'draft']),
          packageId: schema.number.optional(),
          insuranceContractNumber: schema.string(),
          effectiveFrom: schema.date.optional(),
          effectiveTo: schema.date.optional(),
          isPaid: schema.boolean.optional(),
          paymentDate: schema.date.optional(),
          paymentReference: schema.string.optional(),
          annualPremium: schema.number.optional(),
          paymentStatus: schema.enum(['unpaid', 'partial', 'paid']),
          documents: schema.array.optional().members(
            schema.file({
              size: '2mb',
              extnames: ['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
            })
          )
        })
      });
      const {
        contractId, status, packageId, insuranceContractNumber, effectiveFrom, effectiveTo, isPaid, paymentDate, paymentReference, documents,
        annualPremium, paymentStatus,
      } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas autoriser à effectuer cette action";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas autorisé à effectuer cette action";
        return response.status(401).json(apiResponse);
      }
      const contract = await InsuranceContract.query().where('id', contractId).where('insurance_company_id', insurance.id).first();
      if (!contract) {
        apiResponse.message = "Impossible de trouver le contrat d'assurance spécifié. Veuillez vérifier l'identifiant du contrat et réessayer.";
        return response.status(404).json(apiResponse);
      }
      if (contract.status !== 'draft') {
        apiResponse.message = "Cette action n'est pas autorisée. Le contrat doit être en statut 'brouillon' pour pouvoir être modifié. Le statut actuel du contrat est '" + contract.status + "'.";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        if (status === 'validate') {
          let contractDocuments: any[] = [];
          if (documents && documents.length > 0) {
            const storage = new Storage();
            await Promise.all(documents.map(async (doc) => {
              const fileName = new Date().getTime() + "_" + doc.clientName;
              const objectName = `/contracts/${fileName}`;
              const filePath = String(doc.tmpPath);
              let bucketName = 'insurance';
              let metadata = {
                'Content-Type': doc.type,
                'Content-Disposition': `attachment; filename=${fileName}`,
                size: doc.size,
                extension: doc.extname,
              }
              // Upload file to storage
              await storage.uploadFile(bucketName, objectName, filePath, metadata);
              // Add document info to array
              let fileId = await this.generateUUID();
              contractDocuments.push({
                id: fileId,
                name: fileName,
                type: doc.type,
                size: doc.size,
                extension: doc.extname,
                url: objectName,
                uploadedAt: new Date()
              })
            }))
          }
          contract.merge({
            status: status,
            packageId: packageId,
            contractNumber: insuranceContractNumber,
            effectiveFrom: effectiveFrom,
            effectiveTo: effectiveTo,
            isPaid: isPaid,
            paymentDate: paymentDate,
            paymentReference: paymentReference,
            annualPremium: annualPremium,
            paymentStatus: paymentStatus as 'unpaid' | 'partial' | 'paid',
            documents: JSON.stringify(contractDocuments),
          }).useTransaction(trx).save();
          apiResponse.success = true;
          apiResponse.message = "Contrat validé avec succès";
          apiResponse.result = contract;
          await trx.commit();
          return response.status(201).json(apiResponse);
        } else {
          contract.merge({
            status: 'draft',
          }).useTransaction(trx).save();
          apiResponse.success = true;
          apiResponse.message = "Contrat mis à jour avec succès";
          apiResponse.result = contract;
        }
      } catch (error) {
        console.log("error in validate team contract", error);
        await trx.rollback();
        apiResponse.message = "Echec de validation du contrat, une erreur serveur s'est produite";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.log("error in validate team contract", error);
      apiResponse.message = "Echec de validation du contrat, une erreur serveur s'est produite";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async activateInsuranceContract({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          contractId: schema.number(),
          agency_id: schema.number(),
        })
      });
      const {
        contractId, agency_id,
      } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas autoriser à effectuer cette action";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas autorisé à effectuer cette action";
        return response.status(401).json(apiResponse);
      }
      const contract = await InsuranceContract.query().where('id', contractId).where('insurance_company_id', insurance.id).preload('team').firstOrFail();
      if (!contract) {
        apiResponse.message = "Impossible de trouver le contrat d'assurance spécifié. Veuillez vérifier l'identifiant du contrat et réessayer.";
        return response.status(404).json(apiResponse);
      }
      const team = contract.team;
      if (!team) {
        apiResponse.message = "Impossible de trouver le groupe associé au contrat d'assurance. Veuillez vérifier l'identifiant du contrat et réessayer.";
        return response.status(404).json(apiResponse);
      }
      if (contract.status !== 'validate') {
        apiResponse.message = "Cette action n'est pas autorisée. Le contrat doit être en statut 'validé' pour pouvoir être activé. Le statut actuel du contrat est '" + contract.status + "'.";
        return response.status(400).json(apiResponse);
      }

      const insurancePackage = await Package.query().where('id', contract.packageId).where('type', 'team').first();
      if (!insurancePackage) {
        apiResponse.message = "Ce package n'existe pas";
        return response.status(404).json(apiResponse);
      }
      const activeYear = await this.getActiveYear(insurance.id);
      if (!activeYear) {
        apiResponse.message = "Aucune année assurantielle active n'est trouvée";
        return response.status(404).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {

        const teamMembers = await team.related('members').query().where('coverage_status', 'pending').useTransaction(trx);
        const patients = await Promise.all(teamMembers.map(async (teamMember) => {
          return await teamMember.related('patient').query().firstOrFail();
        }));
        const probation_start_at = DateTime.now();
        const probation_end_at = DateTime.now().plus({ months: insurance?.probationDuration ?? 1 });
        const patientInsuranceCompanies = await Promise.all(patients.map(async (patient) => {
          return await patient.related('insurance_companies').create({
            insurance_company_id: insurance.id,
            agencyId: agency_id,
            status: 'validated',
            type_client: 'AP',
            is_pac: false,
            is_active: true,
            validated_at: DateTime.now(),
            probation_start_at: probation_start_at,
            probation_end_at: probation_end_at,
          }, { client: trx });
        }));
        if (!patientInsuranceCompanies) {
          await trx.rollback();
          apiResponse.message = "Echec d'activation du contrat, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        //create subscription for each patient
        let start_date = DateTime.now();
        let end_date = DateTime.now().plus({ months: insurancePackage.validity });
        const subscriptions = await Promise.all(patientInsuranceCompanies.map(async (patientInsurance) => {
          return await patientInsurance.related('subscriptions').create({
            insuranceCompanyId: insurance.id,
            insuranceYearId: activeYear.id,
            packageId: insurancePackage.id,
            patientInsuranceCompanyId: patientInsurance.id,
            patientId: patientInsurance.patient_id,
            startDate: start_date,
            endDate: end_date,
            status: 'active',
            subscriptionType: 'team',
            isUpToDate: true,
            nextDeadline: end_date,
          }, { client: trx });
        }));

        if (!subscriptions) {
          await trx.rollback();
          apiResponse.except = subscriptions;
          apiResponse.message = "Echec d'activation du contrat, une erreur serveur s'est produite";
          return response.status(500).json(apiResponse);
        }

        // Bulk update all team members coverage status and dates in a single query
        await Database
          .from('team_members')
          .whereIn('id', teamMembers.map(member => member.id))
          .useTransaction(trx)
          .update({
            coverage_status: 'active',
            coverage_start: DateTime.now().toSQL(),
            coverage_end: DateTime.now().plus({ year: 1 }).toSQL()
          });
        await contract.merge({
          status: 'active',
          activatedAt: DateTime.now(),
        }).useTransaction(trx).save();
        await team.merge({
          insuranceStatus: 'active',
          hasInsurance: true,
        }).useTransaction(trx).save();

        const eventData: NewTeamSubscriptionData = {
          insurance_year_id: activeYear.id,
          package_id: insurancePackage.id,
          team_id: team.id,
        }
        const nats = await NatService;
        const coverageResponse = await nats.request(
          'team.subscription.created',
          eventData,
          30000
        );

        if (!coverageResponse.success) {
          await trx.rollback();
          apiResponse.message = "Echec de l'activation du contrat, une erreur serveur s'est produite lors de la création de la couverture";
          apiResponse.except = coverageResponse;
          return response.status(500).json(apiResponse);
        }
        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Contrat activé avec succès";
        apiResponse.result = {
          contract: contract,
          coverage: coverageResponse.result,
        };
        return response.status(200).json(apiResponse);
      } catch (error) {
        console.log("error activateContract", error);
        await trx.rollback();
        apiResponse.message = "Echec de l'activation du contrat, une erreur serveur s'est produite";
        apiResponse.except = error;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.log("error in activate team contract", error);
      apiResponse.message = "Echec d'activation du contrat, une erreur serveur s'est produite";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }
}
