import { connect, StringCodec, NatsConnection, Subscription } from 'nats'

class NatService {
  private static instance: NatService
  private nc: NatsConnection | null = null
  private sc = StringCodec()

  private constructor() {}

  public static async getInstance(): Promise<NatService> {
    if (!NatService.instance) {
      NatService.instance = new NatService()
      await NatService.instance.initConnection()
    }
    return NatService.instance
  }


  /**
   * Initialize the NATS connection
   */
  private async initConnection(): Promise<void> {
    if (this.nc) {
      console.log('NATS connection already initialized.')
      return
    }

    try {
      const nat_host = process.env.NATS_TRANSPORTER
      this.nc = await connect({ servers: nat_host })
      console.log('NATS connection initialized.')
    } catch (error) {
      console.error('Error initializing NATS connection:', error)
      throw new Error('Failed to initialize NATS connection')
    }
  }

  /**
   * Publish a message to a NATS subject
   * @param subject - The subject to publish to
   * @param data - The data to publish
   */
  public async publish(subject: string, data: any): Promise<void> {
    if (!this.nc) {
      throw new Error('NATS connection not initialized. Wait for initialization.')
    }

    try {
      const message = this.sc.encode(JSON.stringify(data))
      this.nc.publish(subject, message)
      // console.log(`Message published to subject "${subject}":`, data)
    } catch (error) {
      console.error(`Error publishing to subject "${subject}":`, error)
    }
  }

  public async publishWithAck(subject: string, data: any, timeout = 5000): Promise<boolean> {
    if (!this.nc) {
      throw new Error('NATS connection not initialized. Wait for initialization.')
    }

    try {
      const message = this.sc.encode(JSON.stringify(data))
      const reply = await this.nc.request(subject, message, { timeout })
      return reply !== null
    } catch (error) {
      console.error(`Error publishing to subject "${subject}":`, error)
      return false
    }
  }

  /**
 * Envoie une requête et attend une réponse
 * @param subject - Le sujet NATS
 * @param data - Les données à envoyer
 * @param timeout - Temps d'attente max (ms)
 */
  public async request( subject: string, data: any, timeout = 10000 ): Promise<any> {
    if (!this.nc) {
      throw new Error('NATS connection not initialized.');
    }

    try {
      const message = this.sc.encode(JSON.stringify(data));
      const response = await this.nc.request(subject, message, { timeout });
      return JSON.parse(this.sc.decode(response.data));
    } catch (error) {
      console.error(`Request to "${subject}" failed:`, error);
      throw error;
    }
  }

  /**
   * Subscribe to a NATS subject
   * @param subject - The subject to subscribe to
   * @param callback - The callback function to handle incoming messages
   */
  public async subscribe(subject: string, callback: (message: any) => void): Promise<void> {
    if (!this.nc) {
      throw new Error('NATS connection not initialized. Wait for initialization.')
    }

    try {
      const subscription: Subscription = this.nc.subscribe(subject)

      console.log(`Subscribed to subject "${subject}".`)

      // Process messages from the subscription
      for await (const msg of subscription) {
        const decodedMessage = JSON.parse(this.sc.decode(msg.data))
        console.log(`Message received on subject "${subject}":`, decodedMessage)
        callback(decodedMessage)
      }
    } catch (error) {
      console.error(`Error subscribing to subject "${subject}":`, error)
    }
  }

  /**
   * Close the NATS connection
   */
  public async closeConnection(): Promise<void> {
    if (this.nc) {
      await this.nc.close()
      console.log('NATS connection closed.')
      this.nc = null
    }
  }
}

export default NatService.getInstance()
