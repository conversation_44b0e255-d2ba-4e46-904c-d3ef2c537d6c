import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import InsuranceAdvisorController from './InsuranceAdvisorController';
import { ApiResponse } from 'App/Controllers/interfaces';
import InsuranceCompanyAnalyzeAsk from 'App/Models/InsuranceCompanyAnalyzeAsk';
import AnalyzeAsk from 'App/Models/AnalyzeAsk';
import InsuranceCompanyAnalyzeAskItem from 'App/Models/InsuranceCompanyAnalyzeAskItem';
import { schema } from '@ioc:Adonis/Core/Validator';
import AnalyzeAskItem from 'App/Models/AnalyzeAskItem';
import Database from '@ioc:Adonis/Lucid/Database';
import { DateTime } from 'luxon';

export default class AnalyzeAskController extends InsuranceAdvisorController {

  public async index() {

  }

  public async getPendingAnalyzeAsk({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const insuranceId = request.input('insurance_id', null);
      if (!insuranceId) {
        apiResponse.message = "L'ID de la compagnie d'assurance est requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();

      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const advisor = await this.getAdvisorByUser(authUser.id, insuranceId);
      if (!advisor) {
        apiResponse.message = "Vous n'etes pas élligible à cette compagnie ou votre compte a été suspendue";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyAnalyzeAsk.query().where('insurance_company_id', insuranceId).where('status', 'pending').orderBy('created_at', 'desc').preload('patient').preload('analyze_ask').preload('diagnostic');
      const analyzeAsk = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Liste des analyses récupérées avec succès";
      apiResponse.result = analyzeAsk;
    } catch (error) {
      console.log("error in get pending analyze ask", error);
      apiResponse.message = "Echec de récupération des analyses";
      apiResponse.except = error.message;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getAssuredAnalyzeAskDetails({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;

    try {
      const assuredAnalyzeAskId = request.input('assured_analyze_ask_id');
      if (!assuredAnalyzeAskId) {
        apiResponse.message = "L'ID de l'analyse est requis";
        return response.status(400).json(apiResponse);
      }

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      // Charger la demande d'analyse assurée
      const analyzeAskAssured = await InsuranceCompanyAnalyzeAsk
        .query()
        .where('id', assuredAnalyzeAskId)
        .first();

      if (!analyzeAskAssured) {
        apiResponse.message = "Demande d'analyse assurée introuvable";
        return response.status(404).json(apiResponse);
      }

      // Charger l'analyse principale avec ses relations
      const analyzeAsk = await AnalyzeAsk
        .query()
        .where('id', analyzeAskAssured.analyzeAskId)
        .preload('diagnostic')
        .preload('patient')
        .preload('items', (query) => {
          query.preload('analyze', (query) => {
            query.preload('analyze_type');
          });
        })
        .first();

      if (!analyzeAsk) {
        apiResponse.message = "Analyse introuvable";
        return response.status(404).json(apiResponse);
      }

      // Charger les items assurés liés à cette analyse
      const assuredAnalyzeItems = await InsuranceCompanyAnalyzeAskItem
        .query()
        .where('insurance_company_analyze_ask_id', assuredAnalyzeAskId)
        .preload('analyze_ask_item', (query) => {
          query.preload('analyze', (query) => {
            query.preload('analyze_type');
          });
        });

      // Construire la réponse
      apiResponse.success = true;
      apiResponse.message = "Détails de l'analyse récupérés avec succès";
      apiResponse.result = {
        analyze_ask: analyzeAsk.serialize(),
        assured_analyze_ask: analyzeAskAssured.serialize(),
        assured_analyze_items: assuredAnalyzeItems,
        analyze_ask_items: analyzeAsk.items,
        patient: analyzeAsk.patient
      };

    } catch (error) {
      console.log("error in get analyze ask details", error);
      apiResponse.message = "Échec de récupération des détails de l'analyse";
      apiResponse.except = error.message;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getAssuredAnalyzeAsks({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const insuranceId = request.input('insurance_id', null);
      const filterType = request.input('type') as 'validated' | 'rejected';
      if (!insuranceId) {
        apiResponse.message = "L'ID de la compagnie d'assurance est requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const advisor = await this.getAdvisorByUser(authUser.id, insuranceId);
      if (!advisor) {
        apiResponse.message = "Vous n'etes pas élligible à cette compagnie ou votre compte a été suspendue";
        return response.status(401).json(apiResponse);
      }

      // Construction de la requête de base
      const query = InsuranceCompanyAnalyzeAsk.query()
        .where('insurance_company_id', insuranceId)
        .preload('patient')
        .preload('analyze_ask')
        .preload('diagnostic')
        .preload('items', (query) => {
          query.preload('analyze_ask_item', (query) => {
            query.preload('analyze');
          });
        });

      if (filterType) {
        query.whereIn('status', filterType === 'validated'
          ? ['validated', 'partially_validated']
          : ['rejected', 'partially_validated']);
      }

      // Pagination et exécution
      const analyzeAsks = await query.orderBy('created_at', 'desc').paginate(page, limit);

      // Transformation des résultats
      const transformedResults = analyzeAsks.serialize().data.map(analyzeAsk => {
        const items: InsuranceCompanyAnalyzeAskItem[] = analyzeAsk.items || [];

        const validatedItems = items.filter(item => item.status === 'validated');
        const rejectedItems = items.filter(item => item.status === 'rejected');

        return {
          ...analyzeAsk,
          summary: {
            total_items: items.length,
            items_validated: validatedItems.length,
            items_rejected: rejectedItems.length,
            amount_validated: validatedItems.reduce((sum, item) => sum + (item.totalAmountAssured || 0), 0),
            amount_rejected: rejectedItems.reduce((sum, item) => sum + (item.totalAmountAssured || 0), 0),
            validation_rate: items.length > 0 ? Math.round((validatedItems.length / items.length) * 100) : 0
          }
        };
      });

      apiResponse.success = true;
      apiResponse.message = "Liste des analyses récupérées avec succès";
      apiResponse.result = {
        data: transformedResults,
        meta: analyzeAsks.serialize().meta
      };


    } catch (error) {
      console.log("error in get assured analyze asks", error);
      apiResponse.message = "Echec de récupération des analyses";
      apiResponse.except = error.message;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async validateAnalyzeAsk({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };
    let status = 200;

    try {
      const payload = await request.validate({
        schema: schema.create({
          assured_analyze_ask_id: schema.number(),
          insurance_id: schema.number(),
          notes: schema.string.optional({ trim: true }),
          rejected_reason: schema.string.optional({ trim: true }),
          items: schema.array().members(
            schema.object().members({
              id: schema.number(),
              status: schema.enum(['validated', 'rejected'] as const),
              quantity: schema.number()
            })
          )
        })
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const advisor = await this.getAdvisorByUser(authUser.id, payload.insurance_id);
      if (!advisor) {
        apiResponse.message = "Vous n'êtes pas éligible à cette compagnie ou votre compte a été suspendu";
        return response.status(401).json(apiResponse);
      }

      const insurance_analyze_ask = await InsuranceCompanyAnalyzeAsk
        .query()
        .where('id', payload.assured_analyze_ask_id)
        .first();

      if (!insurance_analyze_ask) {
        apiResponse.message = "Demande d'analyse assurée introuvable";
        return response.status(404).json(apiResponse);
      }

      const analyze_ask = await AnalyzeAsk
        .query()
        .where('id', insurance_analyze_ask.analyzeAskId)
        .preload('items')
        .first();

      if (!analyze_ask) {
        apiResponse.message = "Demande d'analyse introuvable";
        return response.status(404).json(apiResponse);
      }

      const itemIds = payload.items.map(i => i.id);
      const items = await InsuranceCompanyAnalyzeAskItem.query()
        .whereIn('id', itemIds)
        .andWhere('insurance_company_analyze_ask_id', insurance_analyze_ask.id);

      if (items.length !== payload.items.length) {
        apiResponse.message = "Certains items ne sont pas trouvés ou ne correspondent pas à cette demande.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const itemUpdates: InsuranceCompanyAnalyzeAskItem[] = [];
      const analyzeAskItemUpdates: AnalyzeAskItem[] = [];

      let totalItemsValidated = 0;
      let totalItemsRejected = 0;
      let totalAmountValidated = 0;
      let totalAmountRejected = 0;

      const itemMap = new Map(items.map(item => [item.id, item]));

      for (const itemPayload of payload.items) {
        const item = itemMap.get(itemPayload.id);
        if (!item) continue;

        let unitPrice = item.totalQuantityAssured > 0 ? item.totalAmountAssured / item.totalQuantityAssured : 0;

        if (itemPayload.status === 'validated') {
          item.status = 'validated';
          item.totalQuantityAssured = itemPayload.quantity;
          item.totalAmountAssured = itemPayload.quantity * unitPrice;
          item.totalItemsValidated = itemPayload.quantity;
          item.totalAmountValidated = item.totalAmountAssured;
          item.totalItemsRejected = 0;
          item.totalAmountRejected = 0;

          totalItemsValidated += 1;
          totalAmountValidated += item.totalAmountAssured;
        } else {
          item.status = 'rejected';
          item.totalQuantityAssured = 0;
          item.totalAmountAssured = 0;
          item.totalItemsRejected = itemPayload.quantity;
          item.totalAmountRejected = itemPayload.quantity * unitPrice;
          item.totalItemsValidated = 0;
          item.totalAmountValidated = 0;

          totalItemsRejected += 1;
          totalAmountRejected += item.totalAmountRejected;
        }

        itemUpdates.push(item);
      }

      const payloadMap = new Map(payload.items.map(p => [p.id, p]));
      const analyzeItemsMap = new Map(analyze_ask.items.map(ai => [ai.id, ai]));

      for (const item of items) {
        const payloadItem = payloadMap.get(item.id);
        const analyzeItem = analyzeItemsMap.get(item.analyzeAskItemId); // Vérifie que cette colonne existe
        if (!payloadItem || !analyzeItem) continue;

        analyzeItem.can_be_ordered = payloadItem.status === 'validated';
        analyzeItem.is_validated = payloadItem.status === 'validated';
        analyzeItem.validatedAt = payloadItem.status === 'validated' ? DateTime.now() : null;
        analyzeItem.invalidatedAt = payloadItem.status === 'rejected' ? DateTime.now() : null;
        analyzeItem.medecin_conseil_id = advisor.id;

        analyzeAskItemUpdates.push(analyzeItem);
      }

      const trx = await Database.transaction();
      try {
        await Promise.all(itemUpdates.map(item => item.useTransaction(trx).save()));
        await Promise.all(analyzeAskItemUpdates.map(pi => pi.useTransaction(trx).save()));

        insurance_analyze_ask.notes = payload.notes || null;
        insurance_analyze_ask.rejectedReason = payload.rejected_reason || null;
        insurance_analyze_ask.totalItemsValidated = totalItemsValidated;
        insurance_analyze_ask.totalItemsRejected = totalItemsRejected;
        insurance_analyze_ask.totalAmountValidated = totalAmountValidated;
        insurance_analyze_ask.totalAmountRejected = totalAmountRejected;

        insurance_analyze_ask.status = payload.items.every(i => i.status === 'rejected')
          ? 'rejected'
          : payload.items.every(i => i.status === 'validated')
            ? 'validated'
            : 'partially_validated';

        insurance_analyze_ask.validatedBy = advisor.id;
        insurance_analyze_ask.validatedAt = DateTime.now();

        await insurance_analyze_ask.useTransaction(trx).save();

        analyze_ask.validatedAt = DateTime.now();
        await analyze_ask.useTransaction(trx).save();

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = `Analyse ${insurance_analyze_ask.status} avec succès`;
        apiResponse.result = {
          analyzeAskId: insurance_analyze_ask.id,
          status: insurance_analyze_ask.status,
        };

        status = 200;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de la validation", error);
        apiResponse.message = "Erreur lors de la mise à jour des données";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in validate analyze ask", error);
      apiResponse.message = "Échec de validation de l'analyse";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }
}
