import Route from '@ioc:Adonis/Core/Route';
import TeamController from 'App/Controllers/Http/core/admin/TeamController';

const teamCtrl = new TeamController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await teamCtrl.index(ctx);
    });
    Route.get('/details', async (ctx) => {
      return await teamCtrl.getTeamDetails(ctx);
    });
    Route.get('/members', async (ctx) => {
      return await teamCtrl.getTeamMembers(ctx);
    });
    Route.post('/create', async (ctx) => {
      return await teamCtrl.addTeam(ctx);
    });
    Route.post('/contract/create', async (ctx) => {
      return await teamCtrl.createInsuranceContract(ctx);
    });
    Route.post('/contract/validate', async (ctx) => {
      return await teamCtrl.validateInsuranceContract(ctx);
    });
    Route.post('/contract/activate', async (ctx) => {
      return await teamCtrl.activateInsuranceContract(ctx);
    });
  }).prefix('teams');
}).prefix('api').namespace('App/Controllers/Http/core/admin').middleware(['auth:api'])
