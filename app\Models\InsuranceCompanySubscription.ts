import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import PatientInsuranceCompany from './PatientInsuranceCompany'
import InsuranceCompany from './InsuranceCompany'
import Patient from './Patient'
import Package from './Package'

export default class InsuranceCompanySubscription extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patientInsuranceCompanyId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'insurance_year_id' })
  public insuranceYearId: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'used_months' })
  public usedMonths: number | null

  @column({ columnName: 'transaction_id' })
  public transactionId: number | null

  @column({ columnName: 'amount_paid' })
  public amountPaid: number

  @column({ columnName: 'total_amount_paid' })
  public totalAmountPaid: number

  @column({ columnName: 'total_amount_used' })
  public totalAmountUsed: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'active' | 'expired' | 'canceled'

  @column({ columnName: 'is_up_to_date' })
  public isUpToDate: boolean

  @column({ columnName: 'custom_plafond' })
  public customPlafond: any | null

  @column({ columnName: 'subscription_type' })
  public subscriptionType: 'individual' | 'team'

  @column.dateTime({ columnName: 'next_deadline' })
  public nextDeadline: DateTime

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime

  @column({ columnName: 'cancelled_at' })
  public cancelledAt: DateTime

  @column.dateTime({ columnName: 'start_date' })
  public startDate: DateTime

  @column.dateTime({ columnName: 'end_date' })
  public endDate: DateTime

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patientInsuranceCompanyId',
    localKey: 'id',
  })
  public patientInsurance: BelongsTo<typeof PatientInsuranceCompany>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Package, {
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>
}
