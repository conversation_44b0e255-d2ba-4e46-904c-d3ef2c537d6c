import Route from '@ioc:Adonis/Core/Route';
import AgencyController from 'App/Controllers/Http/core/admin/AgencyController';
import InsuranceManagerController from 'App/Controllers/Http/core/admin/InsuranceManagerController';
import InsuranceYearController from 'App/Controllers/Http/core/admin/InsuranceYearController';
import PackageController from 'App/Controllers/Http/core/admin/PackageController';

const agencyCtrl = new AgencyController();
const controller = new InsuranceManagerController();
const packageCtrl = new PackageController();
const yearCtrl = new InsuranceYearController();

Route.group(() => {
  Route.group(() => {
    Route.get('/patients', async (ctx) => {
      return await controller.getPatientInsurance(ctx);
    });
    Route.get('/subscriptions', async (ctx) => {
      return await controller.getInsuranceSubscriptions(ctx);
    });
    Route.get('/fees', async (ctx) => {
      return await controller.getInsuranceFees(ctx);
    });
    Route.get('/prescriptions', async (ctx) => {
      return await controller.getInsurancePrescriptions(ctx);
    });
    Route.get('/analyze-ask', async (ctx) => {
      return await controller.getInsuranceAnalyzeAsk(ctx);
    });
    Route.group(() => {
      Route.group(() => {
        Route.get('/', async (ctx) => {
          return await controller.getInsurancePharmacyPartners(ctx);
        });
        Route.post('/add', async (ctx) => {
          return await controller.addPharmacyPartners(ctx);
        });
      }).prefix('pharmacies');
      Route.group(() => {
        Route.get('/', async (ctx) => {
          return await controller.getInsuranceLaboratoryPartners(ctx);
        });
        Route.post('/add', async (ctx) => {
          return await controller.addLaboratoryPartners(ctx);
        });
      }).prefix('laboratories');
      Route.group(() => {
        Route.get('/', async (ctx) => {
          return await controller.getInsuranceHospitalPartners(ctx);
        });
        Route.post('/add', async (ctx) => {
          return await controller.addHealthInstitutePartners(ctx);
        });
      }).prefix('health-institutes');
    }).prefix('partners');
  }).prefix('company');

  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await agencyCtrl.getAgencies(ctx);
    });
    Route.get('/details', async (ctx) => {
      return await agencyCtrl.getDetails(ctx);
    });
    Route.post('/create', async (ctx) => {
      return await agencyCtrl.addAgency(ctx);
    });
    Route.post('/update', async (ctx) => {
      return await agencyCtrl.updateAgency(ctx);
    });
    Route.get('/managers', async (ctx) => {
      return await agencyCtrl.getInsuranceManagers(ctx);
    });
    Route.post('/managers/create', async (ctx) => {
      return await agencyCtrl.addInsuranceManager(ctx);
    });
  }).prefix('agencies');

  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await packageCtrl.getPackages(ctx);
    });
    Route.get('/details', async (ctx) => {
      return await packageCtrl.getDetails(ctx);
    });
    Route.post('/create', async (ctx) => {
      return await packageCtrl.addPackage(ctx);
    });
    Route.post('/update', async (ctx) => {
      return await packageCtrl.updatePackage(ctx);
    });
    Route.post('/products/add', async (ctx) => {
      return await packageCtrl.addProductToPackage(ctx);
    });
    Route.post('/products/update', async (ctx) => {
      return await packageCtrl.updateProductInPackage(ctx);
    });
    Route.post('/analyzes/add', async (ctx) => {
      return await packageCtrl.addAnalyzesToPackage(ctx);
    });
    Route.post('/analyzes/update', async (ctx) => {
      return await packageCtrl.updateAnalyzeInPackage(ctx);
    });
    Route.post('/publish', async (ctx) => {
      return await packageCtrl.publishedPackage(ctx);
    });
    Route.get('/products', async (ctx) => {
      return await packageCtrl.getPackageProducts(ctx);
    });
    Route.get('/analyzes', async (ctx) => {
      return await packageCtrl.getPackageAnalyzes(ctx);
    });
  }).prefix('packages');

  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await yearCtrl.getInsuranceYears(ctx);
    });
    Route.get('/details', async (ctx) => {
      return await yearCtrl.getInsuranceYearDetails(ctx);
    });
    Route.post('/create', async (ctx) => {
      return await yearCtrl.addNewInsuranceYear(ctx);
    });
    Route.post('/update', async (ctx) => {
      return await yearCtrl.updateInsuranceYear(ctx);
    });
    Route.post('/set-active', async (ctx) => {
      return await yearCtrl.activateInsuranceYearAndUpdatePackages(ctx);
    });
    Route.post('/set-started', async (ctx) => {
      return await yearCtrl.startInsuranceYear(ctx);
    });
    Route.post('/set-finished', async (ctx) => {
      return await yearCtrl.finishedInsuranceYear(ctx);
    });
    Route.post('/save-package-update', async (ctx) => {
      return await yearCtrl.savePackageUpdate(ctx);
    });
    Route.get('/get-active', async (ctx) => {
      return await yearCtrl.getActiveInsuranceYear(ctx);
    });
    Route.get('/pending', async (ctx) => {
      return await yearCtrl.getPendingInsuranceYear(ctx);
    });
  }).prefix('insurance-years');
}).prefix('api').namespace('App/Controllers/Http/core/admin').middleware(['auth:api', 'adminRequest']);
