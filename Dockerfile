ARG NODE_IMAGE=node:20.11.1-alpine

FROM $NODE_IMAGE AS base

WORKDIR /home/<USER>/app

# Copier les fichiers package.json et yarn.lock dans le conteneur
COPY package.json package-lock.json ./

# Installer les dépendances avec npm
RUN npm ci

# Copier le reste des fichiers de l'application
COPY . .

# Définir les variables d'environnement pour le projet
ARG HOST
ARG PORT
ENV HOST=$HOST
ENV PORT=$PORT

# Étape de développement
FROM base AS dev
ENV CHOKIDAR_USEPOLLING=true
ENV NODE_ENV=development
CMD ["node", "ace", "serve", "--watch", "--node-args=--inspect=0.0.0.0"]
