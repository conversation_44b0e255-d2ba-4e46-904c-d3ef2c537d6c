import { DateTime } from 'luxon'
import { BaseModel, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import Product from './Product'
import Analyze from './Analyze'

export default class Package extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column({columnName: 'insurance_company_id'})
  public insuranceCompanyId: number

  @column()
  public price: number | null

  @column()
  public plafond: number | null

  @column()
  public taux: number

  @column()
  public validity: number

  @column({ columnName: 'type' })
  public type: 'individual' | 'team'

  @column({ columnName: 'visibility' })
  public visibility: 'public' | 'private'

  @column()
  public status: string

  @column()
  public payment_type: 'annual' | 'monthly'

  @column()
  public description: string

  @column()
  public settings: any

  @column({ columnName: 'plafond_config' })
  public plafondConfig: any | null

  @column({ columnName: 'fees_config' })
  public feesConfig: any | null

  @column({ columnName: 'tranches_config' })
  public tranchesConfig: any | null

  @column({columnName: 'version'})
  public version: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Product, {
    pivotTable: 'package_products',
    pivotColumns: ['package_id', 'product_id','quantity','public_price','is_active'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'product_id',
    pivotTimestamps: true,
  })
  public products: ManyToMany<typeof Product>

  @manyToMany(() => Analyze, {
    pivotTable: 'package_analyzes',
    pivotColumns: ['package_id', 'analyze_id','public_price','is_active'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'analyze_id',
    pivotTimestamps: true,
  })
  public analyzes: ManyToMany<typeof Analyze>
}
