import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse, PackageStatus, YearStatus } from 'App/Controllers/interfaces';
import HelperController from '../../helpers/HelperController';
import Package from 'App/Models/Package';
import { schema } from '@ioc:Adonis/Core/Validator';
import Product from 'App/Models/Product';
import PackageProduct from 'App/Models/PackageProduct';
import PackageAnalyze from 'App/Models/PackageAnalyze';
import Analyze from 'App/Models/Analyze';
import InsuranceYear from 'App/Models/InsuranceYear';

export default class PackageController extends HelperController {

  public async getPackages({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = Package.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc');
      const packages = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = packages;

    } catch (error) {
      console.log("error in get packages", error);
      apiResponse.except = error;
      apiResponse.message = "Echec de récupération des données";
      apiResponse.result = null;
    }
    return response.status(status).json(apiResponse);
  }

  public async getDetails({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const packageId = request.input('package_id');
      if (!packageId) {
        apiResponse.message = "L'ID du package est requis";
        return response.status(400).json(apiResponse);
      }
      const packageDetail = await Package.query().where('id', packageId).where('insurance_company_id', insurance.id)
        .preload('products')
        .preload('analyzes')
        .first();

      if (!packageDetail || packageDetail.insuranceCompanyId !== insurance.id) {
        apiResponse.message = "Ce package n'existe pas ou n'appartient pas à votre compagnie";
        return response.status(404).json(apiResponse);
      }

      apiResponse.success = true;
      apiResponse.message = "Détails du package récupérés avec succès";
      apiResponse.result = packageDetail;

      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("Error in getDetails", error);
      apiResponse.message = "Échec de récupération des détails du package";
      apiResponse.result = null;
      apiResponse.except = error;

      return response.status(500).json(apiResponse);
    }
  }

  public async getPackageProducts({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const all = request.input('all', false);
      const packageId = request.input('package_id');
      if (!packageId) {
        apiResponse.message = "L'ID du package est requis";
        return response.status(400).json(apiResponse);
      }

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      let query = PackageProduct.query().where('package_id', packageId).where('is_active', true).preload('product');
      const products = all ? await query : await query.paginate(page, limit);

      apiResponse.success = true;
      apiResponse.message = "Détails des produits du package récupérés avec succès";
      apiResponse.result = products;

      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("Error in getPackageProducts", error);
      apiResponse.message = "Échec de récupération des détails du package";
      apiResponse.result = null;
      apiResponse.except = error;

      return response.status(500).json(apiResponse);
    }
  }

  public async getPackageAnalyzes({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    try {
      const packageId = request.input('package_id');
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const all = request.input('all', false);
      if (!packageId) {
        apiResponse.message = "L'ID du package est requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      let query = PackageAnalyze.query().where('package_id', packageId).where('is_active', true).preload('analyze');
      const analyzes = all ? await query : await query.paginate(page, limit);

      apiResponse.success = true;
      apiResponse.message = "Détails des analyses du package récupérés avec succès";
      apiResponse.result = analyzes;

      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("Error in getPackageAnalyzes", error);
      apiResponse.message = "Échec de récupération des détails du package";
      apiResponse.result = null;
      apiResponse.except = error.message;

      return response.status(500).json(apiResponse);
    }
  }

  public async addPackage({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          price: schema.number.optional(),
          plafond: schema.number.optional(),
          taux: schema.number(),
          validity: schema.number(),
          payment_type: schema.enum(['monthly', 'annual']),
          description: schema.string.optional(),
          type: schema.enum(['individual', 'team']),
          visibility: schema.enum(['public', 'private']),
          plafond_config: schema.object.optional().members({
            principal: schema.number(),
            partner: schema.number(),
            child: schema.number()
          }),
          fees_config: schema.object.optional().members({
            principal_fee: schema.number(), // frais pour l'adhérent principale
            partner_fee: schema.number(), // frais pour le partenaire conjoint(e)
            child_fee: schema.number(), // frais pour les enfants
          }),
          tranches_config: schema.object.optional().members({
            nbre_tranches: schema.number(),
            percentages: schema.array().members(schema.number())
          }),
        })
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas autorisé à ajouter un package";
        return response.status(401).json(apiResponse);
      }

      const {
        name, price, plafond, taux, validity, payment_type, description, type, visibility, plafond_config, fees_config, tranches_config,
      } = payload;

      const checkExist = await Package.query().where('name', name).where('insurance_company_id', insurance.id).first();
      if (checkExist) {
        apiResponse.message = "Un package avec ce nom existe déjà";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const newPackage = await Package.create({
        name: name,
        price: price ? price : null,
        plafond: plafond ? plafond : null,
        taux: taux,
        validity: validity,
        type: type === 'individual' ? 'individual' : 'team',
        visibility: visibility === 'public' ? 'public' : 'private',
        plafondConfig: plafond_config ? JSON.stringify(plafond_config) : null,
        feesConfig: fees_config ? JSON.stringify(fees_config) : null,
        tranchesConfig: tranches_config ? JSON.stringify(tranches_config) : null,
        description: description,
        insuranceCompanyId: insurance.id,
        payment_type: payment_type as 'monthly' | 'annual',
        status: PackageStatus.Draft,
        version: '1.0.0',
      });

      if (!newPackage) {
        apiResponse.message = "Echec de création du package";
        apiResponse.except = newPackage;
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Le package a été créé avec succès, passer à l'étape suivante pour ajouter les produits et analyses au package",
        result: newPackage,
      }
      status = 201;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("Error in addPackage", error);
      apiResponse.message = "Échec de l'ajout du package";
      apiResponse.result = null;
    }
    return response.json(apiResponse);
  }

  public async updatePackage({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          name: schema.string.optional(),
          price: schema.number.optional(),
          plafond: schema.number.optional(),
          taux: schema.number.optional(),
          validity: schema.number.optional(),
          payment_type: schema.enum.optional(['monthly', 'annual']),
          description: schema.string.optional(),
          type: schema.enum.optional(['individual', 'team']),
          visibility: schema.enum.optional(['public', 'private']),
          plafond_config: schema.object.optional().members({
            principal: schema.number.nullable(),
            partner: schema.number.nullable(),
            child: schema.number.nullable()
          }),
          fees_config: schema.object.optional().members({
            principal_fee: schema.number.nullable(), // frais pour l'adhérent principale
            partner_fee: schema.number.nullable(), // frais pour le partenaire conjoint(e)
            child_fee: schema.number.nullable(), // frais pour les enfants
          }),
          tranches_config: schema.object.optional().members({
            nbre_tranches: schema.number.nullable(),
            percentages: schema.array.nullable().members(schema.number())
          }),
        })
      });

      const {
        package_id, name, price, plafond, taux, validity, payment_type, description,
        type, visibility, plafond_config, fees_config, tranches_config,
      } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const packageToUpdate = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .firstOrFail();

      if (!packageToUpdate) {
        apiResponse.message = "Package introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      if (![PackageStatus.Draft, PackageStatus.Configured].includes(packageToUpdate.status as PackageStatus)) {
        apiResponse.message = "Ce package ne peut pas être modifié car il est déjà actif. Pour effectuer des modifications, veuillez créer une nouvelle version pour la prochaine période d'assurance.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      packageToUpdate.merge({
        name: name !== undefined ? name : packageToUpdate.name,
        price: price !== undefined ? price : packageToUpdate.price,
        plafond: plafond !== undefined ? plafond : packageToUpdate.plafond,
        taux: taux !== undefined ? taux : packageToUpdate.taux,
        validity: validity !== undefined ? validity : packageToUpdate.validity,
        payment_type: payment_type !== undefined ? payment_type as 'monthly' | 'annual' : packageToUpdate.payment_type,
        description: description !== undefined ? description : packageToUpdate.description,
        type: type !== undefined ? (type as 'individual' | 'team') : packageToUpdate.type,
        visibility: visibility !== undefined ? (visibility as 'public' | 'private') : packageToUpdate.visibility,
        plafondConfig: plafond_config ? JSON.stringify(plafond_config) : packageToUpdate.plafondConfig,
        feesConfig: fees_config ? JSON.stringify(fees_config) : packageToUpdate.feesConfig,
        tranchesConfig: tranches_config ? JSON.stringify(tranches_config) : packageToUpdate.tranchesConfig,
      });
      await packageToUpdate.save();

      apiResponse.success = true;
      apiResponse.message = "Package mis à jour avec succès";
      apiResponse.result = packageToUpdate;

    } catch (error) {
      console.log("error in update package", error);
      apiResponse.except = error;
      apiResponse.message = "Échec de la mise à jour du package";
      apiResponse.result = null;
      status = error.status || 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addProductToPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          products: schema.array().members(
            schema.object().members({
              product_id: schema.number(),
              quantity: schema.number.optional(),
              public_price: schema.number.optional(),
            })
          ),
        }),
      });

      const { package_id, products } = payload;

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas autorisé à ajouter des produits au package";
        return response.status(401).json(apiResponse);
      }

      // Vérification que l'utilisateur est un gestionnaire de compagnie
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas autorisé à ajouter des produits au package";
        return response.status(401).json(apiResponse);
      }

      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .preload('products')
        .firstOrFail();

      if (!insurancePackage) {
        apiResponse.message = "Package introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      // Vérifie si le package est en statut "draft"
      if (insurancePackage.status !== PackageStatus.Draft) {
        apiResponse.message = "Vous ne pouvez pas ajouter de produits à un package qui est en cours d'utilisation";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Filtrage et validation des produits à ajouter
      const productsToAdd = products
        .map((product) => ({
          productId: product.product_id,
          quantity: product.quantity || 1,
          publicPrice: product.public_price || 0,
          isActive: true,
          packageId: insurancePackage.id,
        }))
        .filter(async (product) => {
          //verifier si le prix est !== 0
          if (product.publicPrice === 0) {
            return product.publicPrice = 0;
          }
          // Vérifie si le produit existe
          const checkProdExist = await Product.findBy('id', product.productId);
          if (!checkProdExist) {
            return false; // Ignorer si le produit n'existe pas
          }

          // Vérifie si le produit est déjà lié au package
          const isAlreadyLinked = insurancePackage.products.some(
            (pkgProduct) => pkgProduct.id === product.productId
          );
          return !isAlreadyLinked; // Ajouter seulement s'il n'est pas déjà lié
        });

      const add_product = await PackageProduct.createMany(productsToAdd);
      if (!add_product) {
        apiResponse.message = "Échec de l'ajout des produits au package";
        apiResponse.except = add_product;
        status = 500;
        return response.status(status).json(apiResponse);
      }
      await insurancePackage.merge({
        status: PackageStatus.Configured,
      }).save();

      // Réponse réussie
      apiResponse.success = true;
      apiResponse.message = "Produits ajoutés au package avec succès";
      apiResponse.result = productsToAdd;
    } catch (error) {
      console.error("Error in addProductToPackage", error);
      apiResponse.message = "Échec de l'ajout du produit au package";
      apiResponse.result = null;
      status = error.status || 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addAnalyzesToPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          analyzes: schema.array().members(
            schema.object().members({
              analyze_id: schema.number(),
              public_price: schema.number.optional(), // Optionnel si non fourni
            })
          ),
        }),
      });

      const { package_id, analyzes } = payload;

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      // Vérification que l'utilisateur est un gestionnaire de compagnie
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      // Récupération du package associé à la compagnie d'assurance
      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .preload('analyzes') // Précharge les analyses déjà associées au package
        .firstOrFail();

      if (!insurancePackage) {
        apiResponse.message = "Package introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      if (insurancePackage.status !== PackageStatus.Draft) {
        apiResponse.message = "Vous ne pouvez pas ajouter des analyses à un package qui est déjà en cours d'utilisation";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Filtrage et validation des analyses à ajouter
      const analyzesToAdd = analyzes
        .map((analyze) => ({
          analyzeId: analyze.analyze_id,
          publicPrice: analyze.public_price || 0,
          packageId: insurancePackage.id,
        }))
        .filter(async (analyze) => {
          // Vérifie si le prix est différent de 0
          if (analyze.publicPrice === 0) {
            return false;
          }
          // Vérifie si l'analyse existe
          const checkAnalyzeExist = await Analyze.findBy('id', analyze.analyzeId);
          if (!checkAnalyzeExist) {
            return false; // Ignorer si l'analyse n'existe pas
          }

          // Vérifie si l'analyse est déjà liée au package
          const isAlreadyLinked = insurancePackage.analyzes.some(
            (pkgAnalyze) => pkgAnalyze.id === analyze.analyzeId
          );
          return !isAlreadyLinked; // Ajouter seulement si elle n'est pas déjà liée
        });

      // Création des entrées dans la table pivot PackageAnalyze
      const createdAnalyzes = await PackageAnalyze.createMany(analyzesToAdd);
      if (!createdAnalyzes) {
        apiResponse.message = "Échec de l'ajout des analyses au package";
        apiResponse.except = createdAnalyzes;
        status = 500;
        return response.status(status).json(apiResponse);
      }
      // Mise à jour du statut du package
      await insurancePackage.merge({
        status: PackageStatus.Configured,
      }).save();

      // Réponse réussie
      apiResponse.success = true;
      apiResponse.message = "Analyses ajoutées au package avec succès";
      apiResponse.result = createdAnalyzes.length;
      status = 201;

    } catch (error) {
      console.error("Error in addAnalyzesToPackage", error);
      apiResponse.message = "Échec de l'ajout des analyses au package";
      apiResponse.result = null;
      status = error.status || 500;
    }

    return response.status(status).json(apiResponse);
  }

  public async updateProductInPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };

    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          products: schema.array().members(
            schema.object().members({
              product_id: schema.number(),
              quantity: schema.number.optional(),
              public_price: schema.number(),
            })
          ),
        }),
      });

      const { package_id, products } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .preload('products')
        .firstOrFail();

      if (insurancePackage.status !== PackageStatus.Draft && insurancePackage.status !== PackageStatus.Configured) {
        apiResponse.message = "Vous ne pouvez pas modifier les produits d'un package qui est déjà en cours d'utilisation ou publié";
        return response.status(400).json(apiResponse);
      }

      // Filtrer les produits existants dans le package
      const existingProducts = insurancePackage.products.map((product) => product.id);
      const productsToUpdate = products.filter((product) =>
        existingProducts.includes(product.product_id)
      );

      if (productsToUpdate.length === 0) {
        apiResponse.message = "Aucun produit à mettre à jour";
        return response.json(apiResponse);
      }

      // Construire les conditions et les valeurs pour la mise à jour par lot
      const updates = productsToUpdate.map((product) => ({
        packageId: insurancePackage.id,
        productId: product.product_id,
        quantity: product.quantity || 1,
        publicPrice: product.public_price || 0,
        isActive: true,
      }));

      // Effectuer une mise à jour par lot
      await PackageProduct.query()
        .whereIn('package_id', [insurancePackage.id])
        .whereIn('product_id', updates.map((update) => update.productId))
        .update(updates.map((update) => ({
          quantity: update.quantity,
          publicPrice: update.publicPrice,
          isActive: update.isActive,
        })));

      apiResponse.success = true;
      apiResponse.message = "Produits mis à jour avec succès";
      apiResponse.result = productsToUpdate;
    } catch (error) {
      console.error("Error in updateProductInPackage", error);
      apiResponse.message = "Échec de la mise à jour des produits";
      apiResponse.result = null;
    }

    return response.json(apiResponse);
  }

  public async updateAnalyzeInPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;

    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          analyzes: schema.array().members(
            schema.object().members({
              analyze_id: schema.number(),
              public_price: schema.number(),
              is_active: schema.boolean(),
            })
          ),
        }),
      });

      const { package_id, analyzes } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .preload('analyzes')
        .firstOrFail();

      if (insurancePackage.status !== PackageStatus.Draft) {
        apiResponse.message = "Vous ne pouvez pas modifier les analyses d'un package qui est déjà en cours d'utilisation";
        return response.status(400).json(apiResponse);
      }

      // Filtrer les analyses existantes dans le package
      const existingAnalyzes = insurancePackage.analyzes.map((analyze) => analyze.id);
      const analyzesToUpdate = analyzes.filter((analyze) =>
        existingAnalyzes.includes(analyze.analyze_id)
      );

      if (analyzesToUpdate.length === 0) {
        apiResponse.message = "Aucune analyse à mettre à jour";
        return response.json(apiResponse);
      }

      // Construire les conditions et les valeurs pour la mise à jour par lot
      const updates = analyzesToUpdate.map((analyze) => ({
        packageId: insurancePackage.id,
        analyzeId: analyze.analyze_id,
        publicPrice: analyze.public_price || 0,
        isActive: analyze.is_active || true,
      }));

      // Effectuer une mise à jour par lot
      await PackageAnalyze.query()
        .whereIn('package_id', [insurancePackage.id])
        .whereIn('analyze_id', updates.map((update) => update.analyzeId))
        .update(updates.map((update) => ({
          publicPrice: update.publicPrice,
          isActive: update.isActive,
        })));

      apiResponse.success = true;
      apiResponse.message = "Analyses mis à jour avec succès";
      apiResponse.result = analyzesToUpdate;
      status = 200;
    } catch (error) {
      console.error("Error in updateAnalyzeInPackage", error);
      apiResponse.message = "Échec de la mise à jour des analyses";
      apiResponse.result = null;
      status = error.status || 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async removeProductFromPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          products: schema.array().members(
            schema.object().members({
              product_id: schema.number(),
            })
          ),
        }),
      });
      const { package_id, products } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .preload('products')
        .firstOrFail();
      if (insurancePackage.status !== PackageStatus.Draft) {
        apiResponse.message = "Vous ne pouvez pas supprimer des produits d'un package qui est déjà en cours d'utilisation";
        return response.status(400).json(apiResponse);
      }
      const productIdsToRemove = products.map((product) => product.product_id);
      // Supprimer les produits du package
      await PackageProduct.query()
        .where('package_id', package_id)
        .whereIn('product_id', productIdsToRemove)
        .update({ is_active: false }); // Soft delete by setting is_active to false

      apiResponse.success = true;
      apiResponse.message = "Produits supprimés avec succès";
      apiResponse.result = productIdsToRemove;
      status = 200;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in removeProductFromPackage", error);
      apiResponse.message = "Échec de la suppression du produit";
      apiResponse.result = null;
      status = error.status || 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async removeAnalyzeFromPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          analyzes: schema.array().members(
            schema.object().members({
              analyze_id: schema.number(),
            })
          ),
        }),
      });
      const { package_id, analyzes } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .firstOrFail();
      if (insurancePackage.status !== PackageStatus.Draft) {
        apiResponse.message = "Vous ne pouvez pas supprimer des analyses d'un package qui est déjà en cours d'utilisation";
        return response.status(400).json(apiResponse);
      }

      const analyzeIdsToRemove = analyzes.map((analyze) => analyze.analyze_id);
      // Supprimer les analyses du package
      await PackageAnalyze.query()
        .where('package_id', package_id)
        .whereIn('analyze_id', analyzeIdsToRemove)
        .update({ is_active: false }); // Soft delete by setting is_active to false
      apiResponse.success = true;
      apiResponse.message = "Analyses supprimées avec succès";
      apiResponse.result = analyzeIdsToRemove;
      status = 200;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in removeAnalyzeFromPackage", error);
      apiResponse.message = "Échec de la suppression de l'analyse";
      apiResponse.result = null;
      status = error.status || 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async publishedPackage({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
        }),
      });
      const { package_id } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .firstOrFail();
      if (insurancePackage.status !== PackageStatus.Configured) {
        apiResponse.message = "Vous ne pouvez pas publier un package qui n'est pas encore configuré";
        return response.status(400).json(apiResponse);
      }
      const insuranceYear = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('status', YearStatus.Active)
        .firstOrFail();

      if (!insuranceYear) {
        apiResponse.message = "Vous n'avez pas d'année active";
        return response.status(400).json(apiResponse);
      }

      await insurancePackage.merge({
        status: PackageStatus.Published,
      }).save();

      apiResponse.success = true;
      apiResponse.message = "Package publié avec succès";
      apiResponse.result = insurancePackage;
      status = 200;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in publishedPackage", error);
      apiResponse.message = "Échec de la publication du package";
      apiResponse.result = null;
      status = error.status || 500;
      return response.status(status).json(apiResponse);
    }
  }
}
