import Route from '@ioc:Adonis/Core/Route';
import AnalyzeAskController from 'App/Controllers/Http/core/advisor/AnalyzeAskController';
import InsuranceAdvisorController from 'App/Controllers/Http/core/advisor/InsuranceAdvisorController';
import PrescriptionController from 'App/Controllers/Http/core/advisor/PrescriptionController';

const advisorCtrl = new InsuranceAdvisorController();
const prescriptionCtrl = new PrescriptionController();
const analyzeAskCtrl = new AnalyzeAskController();

Route.group(() => {
  Route.group(() => {
    Route.get('/insurance-companies', async (ctx) => {
      return await advisorCtrl.getAffiliateInsurances(ctx);
    });
    Route.group(() => {
      Route.get('/pending', async (ctx) => {
        return await prescriptionCtrl.getPendingPrescriptions(ctx);
      });
      Route.get('/details', async (ctx) => {
        return await prescriptionCtrl.getAssuredPrescriptionDetails(ctx);
      });
      Route.get('/', async (ctx) => {
        return await prescriptionCtrl.getAssuredPrescriptions(ctx);
      });
      Route.post('/validate', async (ctx) => {
        return await prescriptionCtrl.validateAssuredPrescriptions(ctx);
      });
    }).prefix('prescriptions');
    Route.group(() => {
      Route.get('/pending', async (ctx) => {
        return await analyzeAskCtrl.getPendingAnalyzeAsk(ctx);
      });
      Route.get('/details', async (ctx) => {
        return await analyzeAskCtrl.getAssuredAnalyzeAskDetails(ctx);
      });
      Route.get('/', async (ctx) => {
        return await analyzeAskCtrl.getAssuredAnalyzeAsks(ctx);
      });
      Route.post('/validate', async (ctx) => {
        return await analyzeAskCtrl.validateAnalyzeAsk(ctx);
      });
    }).prefix('analyze-asks');
  }).prefix('advisor');
}).prefix('api').namespace('App/Controllers/Http/core/advisor').middleware('auth:api')
