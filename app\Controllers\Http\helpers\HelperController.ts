// import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { AutoIncrementOptions, VersionType } from "App/Controllers/interfaces";
import InsuranceCompany from "App/Models/InsuranceCompany";
import InsuranceCompanyAgency from "App/Models/InsuranceCompanyAgency";
import InsuranceManager from "App/Models/InsuranceManager";
import InsuranceYear from "App/Models/InsuranceYear";

export default class HelperController {


  public async getManagerByUser(userId: number): Promise<InsuranceManager | null> {
    const insuranceManager = await InsuranceManager.query().where('user_id', userId).preload('agency').preload('company').first()
    if (!insuranceManager) {
      return null
    }
    return insuranceManager;
  }

  public async getInsuranceCompanyByUser(userId: number): Promise<InsuranceCompany | null> {
    const insuranceManager = await this.getManagerByUser(userId);
    if (!insuranceManager) {
      return null
    }
    const insuranceCompany = insuranceManager.company;
    if (!insuranceCompany) {
      return null
    }
    return insuranceCompany
  }

  public async getActiveYear(insuranceId: number): Promise<InsuranceYear | null> {
    const year = await InsuranceYear.query().where('insurance_company_id', insuranceId).where('status', 'started').first();
    if (!year) return null;
    return year;
  }

  public async getAgencyByUser(userId: number): Promise<InsuranceCompanyAgency | null> {
    const insuranceManager = await this.getManagerByUser(userId);
    if (!insuranceManager) {
      return null
    }
    const insuranceAgency = insuranceManager.agency;
    if (!insuranceAgency) {
      return null
    }
    return insuranceAgency
  }

  public async generateWalletCode() {
    const gens = "1234567890";
    let length = 12;
    let code = '';
    for (let i = 0; i < length; i++) {
      code += gens.charAt(Math.floor(Math.random() * gens.length));
    }
    return code;
  }

  public async generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public async incrementVersion(currentVersion: string): Promise<string> {
    const { patchLimit = 10, minorLimit = 5 } = {} as AutoIncrementOptions;

    if (!currentVersion) throw new Error('Version string is required');

    const parts = currentVersion.split('.');
    if (parts.length !== 3 || parts.some(part => !/^\d+$/.test(part))) {
      throw new Error('Version must be in format x.y.z where x, y, z are numbers');
    }

    let [major, minor, patch] = parts.map(Number);

    // Détermination automatique du type d'incrément
    let incrementType: VersionType;

    if (patch >= patchLimit) {
      if (minor >= minorLimit) {
        incrementType = 'major';
      } else {
        incrementType = 'minor';
      }
    } else {
      incrementType = 'patch';
    }

    // Application de l'incrément
    switch (incrementType) {
      case 'major': return `${major + 1}.0.0`;
      case 'minor': return `${major}.${minor + 1}.0`;
      case 'patch': return `${major}.${minor}.${patch + 1}`;
      default: throw new Error('Invalid increment type');
    }
  }

  public async generateCodeParrainage(length: number) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let codeParrainage = '';
    for (let i = 0; i < length; i++) {
      codeParrainage += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return codeParrainage.toUpperCase();
  }

  public async generateToken() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }


}
