import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse } from 'App/Controllers/interfaces'
import HelperController from '../../helpers/HelperController';
import PatientInsuranceCompany from 'App/Models/PatientInsuranceCompany';
import InsuranceCompanySubscription from 'App/Models/InsuranceCompanySubscription';
import InsuranceCompanyFee from 'App/Models/InsuranceCompanyFee';
import InsuranceCompanyPrescription from 'App/Models/InsuranceCompanyPrescription';
import InsuranceCompanyAnalyzeAsk from 'App/Models/InsuranceCompanyAnalyzeAsk';
import { schema } from '@ioc:Adonis/Core/Validator';
import Database from '@ioc:Adonis/Lucid/Database';
import InsuranceCompanyHospital from 'App/Models/InsuranceCompanyHospital';
import InsuranceCompanyPharmacy from 'App/Models/InsuranceCompanyPharmacy';
import InsuranceCompanyLaboratory from 'App/Models/InsuranceCompanyLaboratory';

export default class InsuranceManagerController extends HelperController {

  public async getPatientInsurance({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const agencyId = request.input('agency_id', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const query = PatientInsuranceCompany.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('patient').preload('agency');
      if (agencyId) {
        query.where('insurance_company_agency_id', agencyId);
      }
      const patients = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des patients assurés",
        result: patients
      }

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceSubscriptions({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const agencyId = request.input('agency_id', null);
      const packageId = request.input('package_id', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanySubscription.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('patient').preload('package');
      const filters = {
        insurance_company_agency_id: agencyId,
        package_id: packageId
      };

      if (Object.keys(filters).length > 0) {
        query.where((builder) => {
          for (const key in filters) {
            if (filters.hasOwnProperty(key) && filters[key]) {
              builder.where(key, filters[key]);
            }
          }
        });
      }

      const subscriptions = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = subscriptions;
    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceFees({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const agencyId = request.input('agency_id', null);
      const packageId = request.input('package_id', null);
      const type_cotisation = request.input('type_cotisation', null);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyFee.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('patient').preload('package').preload('agency').preload('subscription');
      const filters = {
        insurance_company_agency_id: agencyId,
        package_id: packageId,
        type_cotisation: type_cotisation
      };

      if (Object.keys(filters).length > 0) {
        query.where((builder) => {
          for (const key in filters) {
            if (filters.hasOwnProperty(key) && filters[key]) {
              builder.where(key, filters[key]);
            }
          }
        });
      }

      const fees = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = fees;
    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsurancePrescriptions({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyPrescription.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('patient')
      const patients = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = patients;

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceAnalyzeAsk({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyAnalyzeAsk.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('patient')
      const analyzes = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = analyzes;

    } catch (error) {
      console.log("error in get insurance analyze ask", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addHealthInstitutePartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      const payload = await request.validate({
        schema: schema.create({
          health_institutes: schema.array().members(
            schema.object().members({
              health_institute_id: schema.number(),
              is_active: schema.boolean.optional(),
            })
          ),
        }),
      });

      const { health_institutes } = payload;

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      // Vérification que l'utilisateur est un gestionnaire de compagnie
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const existingPartnersQuery = await InsuranceCompanyHospital.query()
        .where('insurance_company_id', insurance.id)
        .select('health_institute_id');

      const existingPartners = existingPartnersQuery.map((partner) => partner.healthInstituteId);
      // Démarrer une transaction
      const trx = await Database.transaction();

      try {

        // Filtrer les nouveaux partenaires à ajouter
        const partnersToAdd = health_institutes
          .filter((partner) => !existingPartners.includes(partner.health_institute_id))
          .map((partner) => ({
            healthInstituteId: partner.health_institute_id,
            isActive: partner.is_active || true,
            insuranceCompanyId: insurance.id,
          }));

        if (partnersToAdd.length === 0) {
          apiResponse.message = "Tous les partenaires existent déjà";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        // Ajouter les nouveaux partenaires en utilisant createMany
        const newPartners = await InsuranceCompanyHospital.createMany(partnersToAdd, trx);

        // Valider la transaction
        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Partenaires ajoutés avec succès";
        apiResponse.result = newPartners;

        return response.status(status).json(apiResponse);
      } catch (error) {
        // Annuler la transaction en cas d'erreur
        await trx.rollback();
        console.error("Error in addHealthInstitutePartners", error);
        apiResponse.message = "Échec de l'ajout des partenaires";
        apiResponse.except = error;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.error("Error in addHealthInstitutePartners", error);
      apiResponse.message = "Echec de l'ajout du partenaire";
      apiResponse.except = error;
      status = 500;
      return response.status(status).json(apiResponse);
    }

  }

  public async addPharmacyPartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          pharmacies: schema.array().members(
            schema.object().members({
              pharmacy_id: schema.number(),
              is_active: schema.boolean.optional(),
            })
          ),
        }),
      });

      const { pharmacies } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      // Récupérer les IDs des pharmacies déjà existantes pour cette compagnie
      const existingPharmaciesQuery = await InsuranceCompanyPharmacy.query()
        .where('insurance_company_id', insurance.id)
        .select('pharmacy_id');

      const existingPharmacies = existingPharmaciesQuery.map(
        (pharmacy) => pharmacy.pharmacyId
      );
      const trx = await Database.transaction();

      try {

        const pharmaciesToAdd = pharmacies
          .filter((pharmacy) => !existingPharmacies.includes(pharmacy.pharmacy_id))
          .map((pharmacy) => ({
            pharmacyId: pharmacy.pharmacy_id,
            isActive: pharmacy.is_active || true,
            insuranceCompanyId: insurance.id,
          }));

        if (pharmaciesToAdd.length === 0) {
          apiResponse.message = "Toutes les pharmacies existent déjà";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        const newPharmacies = await InsuranceCompanyPharmacy.createMany(pharmaciesToAdd, { client: trx });

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Pharmacies ajoutées avec succès";
        apiResponse.result = newPharmacies;

        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.error("Error in addPharmacyPartners", error);
        apiResponse.message = "Échec de l'ajout des pharmacies";
        apiResponse.except = error;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.error("Error in addPharmacyPartners", error);
      apiResponse.message = "Echec de l'ajout des pharmacies";
      apiResponse.except = error;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async addLaboratoryPartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          laboratories: schema.array().members(
            schema.object().members({
              laboratory_id: schema.number(),
              is_active: schema.boolean.optional(),
            })
          ),
        }),
      });

      const { laboratories } = payload;

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      // Vérification que l'utilisateur est un gestionnaire de compagnie
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      // Démarrer une transaction
      const existingLaboratoriesQuery = await InsuranceCompanyLaboratory.query()
        .where('insurance_company_id', insurance.id)
        .select('laboratory_id');

      const existingLaboratories = existingLaboratoriesQuery.map(
        (laboratory) => laboratory.laboratoryId
      );
      const trx = await Database.transaction();

      try {

        const laboratoriesToAdd = laboratories
          .filter((laboratory) => !existingLaboratories.includes(laboratory.laboratory_id))
          .map((laboratory) => ({
            laboratoryId: laboratory.laboratory_id,
            isActive: laboratory.is_active || true,
            insuranceCompanyId: insurance.id,
          }));

        if (laboratoriesToAdd.length === 0) {
          apiResponse.message = "Tous les laboratoires existent déjà";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        const newLaboratories = await InsuranceCompanyLaboratory.createMany(laboratoriesToAdd, { client: trx });

        // Valider la transaction
        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Laboratoires ajoutés avec succès";
        apiResponse.result = newLaboratories;
      } catch (error) {
        // Annuler la transaction en cas d'erreur
        await trx.rollback();
        console.error("Error in addLaboratoryPartners", error);
        apiResponse.message = "Échec de l'ajout des laboratoires";
        apiResponse.except = error;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.error("Error in addLaboratoryPartners", error);
      apiResponse.message = "Echec de l'ajout des laboratoires";
      apiResponse.except = error;
      status = 500;
      return response.status(status).json(apiResponse);
    }

    return response.status(status).json(apiResponse);
  }

  public async getInsuranceHospitalPartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyHospital.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('healthInstitute');
      const partners = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = partners;

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsurancePharmacyPartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyPharmacy.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('pharmacy');
      const partners = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = partners;

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceLaboratoryPartners({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyLaboratory.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc').preload('laboratory');
      const partners = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = partners;

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

}
