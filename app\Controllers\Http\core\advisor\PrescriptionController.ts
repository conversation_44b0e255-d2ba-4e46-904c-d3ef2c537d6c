import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import InsuranceAdvisorController from "./InsuranceAdvisorController";
import { ApiResponse } from 'App/Controllers/interfaces';
import InsuranceCompanyPrescription from 'App/Models/InsuranceCompanyPrescription';
import Prescription from 'App/Models/Prescription';
import InsuranceCompanyPrescriptionItem from '../../../../Models/InsuranceCompanyPrescriptionItem';
import { schema } from '@ioc:Adonis/Core/Validator';
import { DateTime } from 'luxon';
import Database from '@ioc:Adonis/Lucid/Database';
import PrescriptionItem from 'App/Models/PrescriptionItem';

export default class PrescriptionController extends InsuranceAdvisorController {

  public async getPendingPrescriptions({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const insuranceId = request.input('insurance_id', null);
      if (!insuranceId) {
        apiResponse.message = "L'ID de la compagnie d'assurance est requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();

      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const advisor = await this.getAdvisorByUser(authUser.id, insuranceId);
      if (!advisor) {
        apiResponse.message = "Vous n'etes pas élligible à cette compagnie ou votre compte a été suspendue";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceCompanyPrescription.query().where('insurance_company_id', insuranceId).where('status', 'pending').orderBy('created_at', 'desc')
        .preload('patient')
        .preload('prescription')
        .preload('diagnostic');

      const prescriptions = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Liste des prescriptions récupérées avec succès";
      apiResponse.result = prescriptions;

    } catch (error) {
      console.log("error in get pending prescriptions", error);
      apiResponse.message = "Echec de récupération des prescriptions";
      apiResponse.except = error.message;
      status = 500;
    } finally {
      return response.status(status).json(apiResponse);
    }
  }

  /**
   * getAssuredPrescriptionDetails
   * Retourner les détails d'une prescription assurée
   * @param {HttpContextContract} {request,auth,response}
   * @return {*}
   * @memberof PrescriptionController
   */
  public async getAssuredPrescriptionDetails({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const assuredPrescriptionId = request.input('assured_prescription_id');
      if (!assuredPrescriptionId) {
        apiResponse.message = "L'ID de la prescription est requis";
        return response.status(400).json(apiResponse);
      }
      const authUser = await auth.authenticate();

      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const prescription_assured = await InsuranceCompanyPrescription.query().where('id', assuredPrescriptionId).first();
      if (!prescription_assured) {
        apiResponse.message = "Prescription introuvable";
        return response.status(404).json(apiResponse);
      }
      const prescription = await Prescription.query().where('id', prescription_assured.prescriptionId)
        .preload('pro')
        .preload('diagnostic')
        .preload('patient')
        .preload('items', (query) => {
          query.preload('product');
        }).first();
      if (!prescription) {
        apiResponse.message = "Prescription introuvable";
        return response.status(404).json(apiResponse);
      }

      const assured_prescription_items = await InsuranceCompanyPrescriptionItem.query().where('insurance_company_prescription_id', assuredPrescriptionId)
        .preload('prescription_item', (query) => {
          query.preload('product');
        });

      apiResponse.success = true;
      apiResponse.message = "Détails de la prescription récupérés avec succès";
      apiResponse.result = {
        prescription: prescription.serialize(),
        assured_prescription: prescription_assured.serialize(),
        assured_prescription_items: assured_prescription_items,
        prescription_items: prescription.items,
        patient: prescription.patient
      };
    } catch (error) {
      console.log("error in get prescription details", error);
      apiResponse.message = "Echec de récupération des détails de la prescription";
      apiResponse.except = error.message;
      status = 500;
    }
    finally {
      return response.status(status).json(apiResponse);
    }
  }

  public async getAssuredPrescriptions({ request, auth, response }: HttpContextContract) {
    try {
      // Authentification et validation
      const user = await auth.authenticate();
      const insuranceId = request.input('insurance_id');
      const filterType = request.input('type') as 'validated' | 'rejected'; // 'validated' ou 'rejected'
      const page = request.input('page', 1);
      const limit = request.input('limit', 15);

      if (!insuranceId) {
        return response.badRequest({ message: "Paramètres manquants" });
      }

      // Vérification des droits
      const advisor = await this.getAdvisorByUser(user.id, insuranceId);
      if (!advisor) {
        return response.unauthorized({ message: "Accès non autorisé" });
      }

      // Construction de la requête de base
      const query = InsuranceCompanyPrescription.query().orderBy('created_at', 'desc')
        .where('insurance_company_id', insuranceId)
        .preload('patient')
        .preload('diagnostic')
        .preload('prescription')
        .preload('items', (query) => {
          query.preload('prescription_item', (query) => {
            query.preload('product');
          });
        });

      if (filterType) {
        query.whereIn('status', filterType === 'validated'
          ? ['validated', 'partially_validated']
          : ['rejected', 'partially_validated']);
      }

      // Pagination et exécution
      const prescriptions = await query.paginate(page, limit);

      // Transformation des résultats
      const transformedResults = prescriptions.serialize().data.map(prescription => {
        const items: InsuranceCompanyPrescriptionItem[] = prescription.items || [];

        const validatedItems = items.filter(item => item.status === 'validated');
        const rejectedItems = items.filter(item => item.status === 'rejected');

        return {
          ...prescription,
          summary: {
            total_items: items.length,
            items_validated: validatedItems.length,
            items_rejected: rejectedItems.length,
            amount_validated: validatedItems.reduce((sum, item) => sum + (item.totalAmountValidated || 0), 0),
            amount_rejected: rejectedItems.reduce((sum, item) => sum + (item.totalAmountRejected || 0), 0),
            validation_rate: items.length > 0 ? Math.round((validatedItems.length / items.length) * 100) : 0
          }
        };
      });

      return response.json({
        success: true,
        message: "Etat des prescriptions récupérés avec succès",
        result: {
          data: transformedResults,
          meta: prescriptions.serialize().meta
        }
      });

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: "Erreur lors de la récupération des prescriptions",
        error: error.message
      });
    }
  }


  public async validateAssuredPrescriptions({ request, auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
      errors: null
    };
    let status = 200;

    try {
      // 1. Valider le payload
      const payload = await request.validate({
        schema: schema.create({
          assured_prescription_id: schema.number(),
          insurance_id: schema.number(),
          notes: schema.string.optional({ trim: true }),
          rejected_reason: schema.string.optional({ trim: true }),
          items: schema.array().members(
            schema.object().members({
              id: schema.number(),
              status: schema.enum(['validated', 'rejected'] as const),
              quantity: schema.number()
            })
          )
        })
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const advisor = await this.getAdvisorByUser(authUser.id, payload.insurance_id);
      if (!advisor) {
        apiResponse.message = "Vous n'êtes pas éligible à cette compagnie ou votre compte a été suspendu";
        return response.status(401).json(apiResponse);
      }

      // 2. Charger la prescription assurée
      const insurance_prescription = await InsuranceCompanyPrescription
        .query()
        .where('id', payload.assured_prescription_id)
        .first();

      if (!insurance_prescription) {
        apiResponse.message = "Prescription assurée introuvable";
        return response.status(404).json(apiResponse);
      }

      const prescription = await Prescription
        .query()
        .where('id', insurance_prescription.prescriptionId)
        .preload('items')
        .first();

      if (!prescription) {
        apiResponse.message = "Prescription introuvable";
        return response.status(404).json(apiResponse);
      }

      // 3. Charger les items liés
      const itemIds = payload.items.map(i => i.id);
      const items = await InsuranceCompanyPrescriptionItem
        .query()
        .whereIn('id', itemIds)
        .andWhere('insurance_company_prescription_id', insurance_prescription.id);

      if (items.length !== payload.items.length) {
        apiResponse.message = "Certains items ne sont pas trouvés ou ne correspondent pas à cette prescription.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // 4. Préparer les mises à jour par batch
      const itemUpdates: InsuranceCompanyPrescriptionItem[] = [];
      const prescriptionItemUpdates: PrescriptionItem[] = [];

      let totalItemsValidated = 0;
      let totalItemsRejected = 0;
      let totalAmountValidated = 0;
      let totalAmountRejected = 0;

      const itemMap = new Map(items.map(item => [item.id, item]));

      for (const itemPayload of payload.items) {
        const item = itemMap.get(itemPayload.id);
        if (!item) continue;

        let unitPrice = item.totalQuantityAssured > 0 ? item.totalAmountAssured / item.totalQuantityAssured : 0;

        if (itemPayload.status === 'validated') {
          item.status = 'validated';
          item.totalQuantityAssured = itemPayload.quantity;
          item.totalAmountAssured = itemPayload.quantity * unitPrice;
          item.totalItemsValidated = itemPayload.quantity;
          item.totalAmountValidated = item.totalAmountAssured;
          item.totalItemsRejected = itemPayload.quantity;
          item.totalAmountRejected = itemPayload.quantity * unitPrice;

          totalItemsValidated += 1;
          totalAmountValidated += item.totalAmountAssured;
        } else {
          item.status = 'rejected';
          item.totalQuantityAssured = itemPayload.quantity;
          item.totalAmountAssured = itemPayload.quantity * unitPrice;
          item.totalItemsRejected = itemPayload.quantity;
          item.totalAmountRejected = itemPayload.quantity * unitPrice;
          item.totalItemsValidated = itemPayload.quantity;
          item.totalAmountValidated = itemPayload.quantity * unitPrice;

          totalItemsRejected += 1;
          totalAmountRejected += item.totalAmountRejected;
        }

        itemUpdates.push(item);
      }

      // Mappage entre prescription_item.id → payload item
      const payloadMap = new Map(payload.items.map(p => [p.id, p]));
      const prescripItemsMap = new Map(prescription.items.map(pi => [pi.id, pi]));

      for (const item of items) {
        const payloadItem = payloadMap.get(item.id);
        const prescriptionItem = prescripItemsMap.get(item.prescriptionItemId);
        if (!payloadItem || !prescriptionItem) continue;

        prescriptionItem.can_be_ordered = payloadItem.status === 'validated';
        prescriptionItem.is_validated = payloadItem.status === 'validated';
        prescriptionItem.validatedAt = payloadItem.status === 'validated' ? DateTime.now() : null;
        prescriptionItem.invalidatedAt = payloadItem.status === 'rejected' ? DateTime.now() : null;
        prescriptionItem.medecin_conseil_id = advisor.id;

        prescriptionItemUpdates.push(prescriptionItem);
      }

      // 5. Transaction globale
      const trx = await Database.transaction();
      try {
        // Mettre à jour les items par batch
        await Promise.all(itemUpdates.map(item => item.useTransaction(trx).save()));

        // Mettre à jour prescription_item par batch
        await Promise.all(prescriptionItemUpdates.map(pi => pi.useTransaction(trx).save()));

        // Mettre à jour la prescription assurée
        insurance_prescription.notes = payload.notes || null;
        insurance_prescription.rejectedReason = payload.rejected_reason || null;
        insurance_prescription.totalItemsValidated = totalItemsValidated;
        insurance_prescription.totalItemsRejected = totalItemsRejected;
        insurance_prescription.totalAmountValidated = totalAmountValidated;
        insurance_prescription.totalAmountRejected = totalAmountRejected;
        insurance_prescription.status = payload.items.every(i => i.status === 'rejected')
          ? 'rejected'
          : payload.items.every(i => i.status === 'validated')
            ? 'validated'
            : 'partially_validated';

        insurance_prescription.validatedBy = advisor.id;
        insurance_prescription.validatedAt = DateTime.now();

        await insurance_prescription.useTransaction(trx).save();

        // Mettre à jour la prescription principale
        prescription.validatedAt = DateTime.now();
        prescription.isOrdered = true;

        await prescription.useTransaction(trx).save();

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = `Prescription ${insurance_prescription.status} avec succès`;
        apiResponse.result = {
          prescriptionId: insurance_prescription.id,
          status: insurance_prescription.status,
        };

        status = 200;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de la transaction", error);
        apiResponse.message = "Erreur lors de la mise à jour des items";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("Erreur dans validate assured prescriptions", error);
      apiResponse.message = "Échec, une erreur serveur est survenue lors de la validation des prescriptions";
      apiResponse.except = error.message;
      apiResponse.errors = error.messages;
      status = 500;
      return response.status(status).json(apiResponse);
    }

  }

}
