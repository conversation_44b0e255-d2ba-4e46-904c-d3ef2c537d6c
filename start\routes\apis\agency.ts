import Route from '@ioc:Adonis/Core/Route';
import InsuranceAgencyController from 'App/Controllers/Http/core/agency/InsuranceAgencyController';
import PatientInsuranceController from 'App/Controllers/Http/core/agency/PatientInsuranceController';

const ptCtrl = new PatientInsuranceController();
const agcyCtrl = new InsuranceAgencyController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return ctx.response.json({ message: "Hello World" })
    });
    Route.group(() => {
      Route.get('/', async (ctx) => {
        return await ptCtrl.getPatientInsurance(ctx);
      });
      Route.get('/beneficiaries', async (ctx) => {
        return await ptCtrl.getInsuranceBeneficiaries(ctx);
      });
      Route.get('/requests', async (ctx) => {
        return await ptCtrl.getInsuranceRequests(ctx);
      });
      Route.get('/insurance-data', async (ctx) => {
        return await ptCtrl.getPatientInsuranceData(ctx);
      });
      Route.get('/beneficiaries/data', async (ctx) => {
        return await ptCtrl.getBeneficiaryData(ctx);
      });
      Route.post('/validate', async (ctx) => {
        return await ptCtrl.validateInsuranceRequest(ctx);
      });
      Route.post('/beneficiaries/validate', async (ctx) => {
        return await ptCtrl.validateBeneficiary(ctx);
      });
    }).prefix('patients');
    Route.group(() => {
      Route.get('/subscriptions', async (ctx) => {
        return await agcyCtrl.getInsuranceSubscriptions(ctx);
      });
      Route.get('/cotisation-fees', async (ctx) => {
        return await agcyCtrl.getInsuranceFees(ctx);
      });
    }).prefix('payments');
  }).prefix('agency');
}).prefix('api').namespace('App/Controllers/Http/core/agency').middleware('auth:api')
