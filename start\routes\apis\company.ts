import Route from '@ioc:Adonis/Core/Route';
import InsuranceCompanyController from 'App/Controllers/Http/core/InsuranceCompanyController';

const companyCtrl = new InsuranceCompanyController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return await companyCtrl.getCompanyProfile(ctx)
    })
  }).prefix('company');
}).prefix('api').namespace('App/Controllers/Http/core').middleware('auth:api')
