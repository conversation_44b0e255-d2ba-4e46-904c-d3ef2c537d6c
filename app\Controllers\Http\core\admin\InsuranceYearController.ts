import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from '../../helpers/HelperController';
import { AnalyzeToUpdate, ApiResponse, PackageStatus, ProductToUpdate, YearStatus } from 'App/Controllers/interfaces';
import InsuranceYear from 'App/Models/InsuranceYear';
import { schema } from '@ioc:Adonis/Core/Validator';
import { DateTime } from 'luxon';
import Package from 'App/Models/Package';
import PackageUpdate from 'App/Models/PackageUpdate';
import Database from '@ioc:Adonis/Lucid/Database';

export default class InsuranceYearController extends HelperController {

  public async getInsuranceYears({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      const query = InsuranceYear.query().where('insurance_company_id', insurance.id).orderBy('created_at', 'desc');
      const years = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Données de la compagnie recuperées avec succès";
      apiResponse.result = years;
    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceYearDetails({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;
    try {
      const yearId = request.input('year_id');
      if (!yearId) {
        apiResponse.message = "L'année n'est pas spécifiée";
        return response.status(400).json(apiResponse);
      }

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const year = await InsuranceYear.query().where('id', yearId).where('insurance_company_id', insurance.id).first();
      if (!year) {
        apiResponse.message = "Année assurantielle introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle récupérée avec succès";
      apiResponse.result = year;

    } catch (error) {
      console.log("error in get company patients", error);
      apiResponse.message = "Echec de récupération des données de la compagnie";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getActiveInsuranceYear({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const currentYear = new Date().getFullYear();
      const year = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('status', YearStatus.Started)
        .where('year', currentYear)
        .orderBy('created_at', 'desc')
        .first();
      if (!year) {
        apiResponse.message = "Aucune année assurantielle démarrée trouvée";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle démarrée récupérée avec succès";
      apiResponse.result = year;
    } catch (error) {
      console.log("error in get active insurance year", error);
      apiResponse.message = "Echec de la récupération de l'année assurantielle active";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getPendingInsuranceYear({ response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      const year = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('status', YearStatus.Pending)
        .orderBy('created_at', 'desc')
        .first();
      
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle en attente récupérée avec succès";
      apiResponse.result = year;
    } catch (error) {
      console.log("error in get pending insurance year", error);
      apiResponse.message = "Echec de la récupération de l'année assurantielle en attente";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addNewInsuranceYear({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 201;

    try {
      // Validation du payload
      const payload = await request.validate({
        schema: schema.create({
          libelle: schema.string(),
          type_year: schema.enum(['civil', 'commercial']), // Régime : civile ou commerciale
          year: schema.number(), // Année concernée
          start_at: schema.date.optional(), // Date de début (pour année commerciale)
          end_at: schema.date.optional(), // Date de fin (pour année commerciale)
          adhesion_fee_required: schema.boolean(),
          adhesion_type: schema.enum(['renewable', 'unique']),
          adhesion_price: schema.number(),
          beneficiary_config: schema.object().members({
            enabled: schema.boolean(),
            limit: schema.number(),
            birthdate: schema.object().members({
              max: schema.number(),
              min: schema.number()
            })
          }),
          complementary_config: schema.object.optional().members({
            enabled: schema.boolean(),
            mode: schema.string.nullable(),
            coverage_rate: schema.number.nullable()
          }),
          refund_config: schema.object.optional().members({
            enabled: schema.boolean(),
            taux: schema.number(),
            delay: schema.number()
          }),
        }),
        messages: {
          'type_year.required': "Le type d'année (civile ou commerciale) est requis.",
          'year.required': "L'année est requise.",
          'start_at.requiredWhen': "La date de début est requise pour une année commerciale.",
          'end_at.requiredWhen': "La date de fin est requise pour une année commerciale.",
        }
      });

      const {
        libelle, type_year, year, start_at, end_at, adhesion_price, beneficiary_config, complementary_config, refund_config, adhesion_type, adhesion_fee_required
      } = payload;

      // Authentification de l'utilisateur
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      // Vérification que l'utilisateur gère une compagnie d'assurance
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      // Vérification de l'existence d'une AA active ou planifiée
      const currentDate = DateTime.now();
      const currentYear = currentDate.year;
      const nextYear = currentYear + 1;

      if (year < currentYear || year > nextYear) {
        apiResponse.message = "L'année spécifiée doit être entre l'année en cours ou la suivante.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Check if there is already a pending insurance year
      const pendingYear = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('status', 'pending')
        .first();

      if (pendingYear) {
        apiResponse.message = "Une année assurantielle est déjà en attente. Veuillez l'activer ou la supprimer avant d'en créer une nouvelle.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Calcul des dates de début et de fin
      let startDate, endDate;

      if (type_year === 'civil') {
        // Année civile : Dates fixes
        if (year === currentYear) {
          startDate = currentDate;
          endDate = DateTime.fromObject({ year: currentYear, month: 12, day: 31 });
        } else if (year === nextYear) {
          startDate = DateTime.fromObject({ year: nextYear, month: 1, day: 1 });
          endDate = DateTime.fromObject({ year: nextYear, month: 12, day: 31 });
        } else {
          apiResponse.message = "L'année spécifiée n'est pas valide. Elle doit être l'année en cours ou la suivante.";
          status = 400;
          return response.status(status).json(apiResponse);
        }
      } else if (type_year === 'commercial') {
        // Année commerciale : Dates personnalisées
        if (!start_at || !end_at) {
          apiResponse.message = "Les dates de début et de fin sont requises pour une année commerciale.";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        if (start_at > end_at) {
          apiResponse.message = "La date de début doit être antérieure à la date de fin.";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        // Check if period is exactly one year
        if (start_at && end_at) {
          const startDateTime = start_at;
          const endDateTime = end_at;
          const diffInDays = endDateTime.diff(startDateTime, 'days').days;

          if (Math.floor(diffInDays) !== 365 && Math.floor(diffInDays) !== 366) { // Account for leap years
            apiResponse.message = "La période doit être exactement d'un an (365 ou 366 jours).";
            status = 400;
            return response.status(status).json(apiResponse);
          }

        }
        startDate = start_at;
        endDate = end_at;
      }
      // Vérification des chevauchements
      const overlappingYear = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('start_at', '<=', endDate.toISODate())
        .where('end_at', '>=', startDate.toISODate())
        .first();

      if (overlappingYear) {
        apiResponse.message = "Une année assurantielle chevauche déjà cette période.";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      // Création de la nouvelle année assurantielle
      const uuid = await this.generateUUID();
      const newYear = await InsuranceYear.create({
        uuid: uuid,
        insuranceCompanyId: insurance.id,
        libelle: libelle || `Année ${year}`,
        typeYear: type_year,
        year: year,
        startAt: startDate,
        endAt: endDate,
        adhesionType: adhesion_type as 'renewable' | 'unique',
        adhesionPrice: adhesion_price,
        adhesionFeeRequired: adhesion_fee_required,
        beneficiaryConfig: JSON.stringify(beneficiary_config),
        complementaryConfig: JSON.stringify(complementary_config),
        refundConfig: JSON.stringify(refund_config),
        status: YearStatus.Pending,
        createdBy: authUser.id
      });

      if (!newYear) {
        apiResponse.message = "Échec de création de l'année assurantielle";
        apiResponse.except = newYear;
        status = 500;
        return response.status(status).json(apiResponse);
      }

      // Réponse réussie
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle ajoutée avec succès";
      apiResponse.result = newYear;
      return response.status(status).json(apiResponse);

    } catch (error) {
      console.log("Erreur lors de l'ajout de l'année assurantielle :", error);
      apiResponse.message = "Échec de l'ajout de l'année assurantielle";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async updateInsuranceYear({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };
    let status = 200;

    try {
      const payload = await request.validate({
        schema: schema.create({
          year_id: schema.number(),
          libelle: schema.string.optional(),
          type_year: schema.enum.optional(['civil', 'commercial']),
          year: schema.number.optional(),
          start_at: schema.date.optional(),
          end_at: schema.date.optional(),
          adhesion_type: schema.enum.optional(['renewable', 'unique']),
          adhesion_price: schema.number.optional(),
          adhesion_fee_required: schema.boolean.optional(),
          beneficiary_config: schema.object.optional().members({
            enabled: schema.boolean(),
            limit: schema.number(),
            birthdate: schema.object().members({
              max: schema.number(),
              min: schema.number()
            })
          }),
          complementary_config: schema.object.optional().members({
            enabled: schema.boolean(),
            mode: schema.string.nullable(),
            coverage_rate: schema.number.nullable()
          }),
          refund_config: schema.object.optional().members({
            enabled: schema.boolean(),
            taux: schema.number(),
            delay: schema.number()
          }),
        }),
        messages: {
          'type_year.requiredWhen': "Le type d'année est requis pour une modification.",
          'start_at.requiredWhen': "La date de début est requise pour une année commerciale.",
          'end_at.requiredWhen': "La date de fin est requise pour une année commerciale.",
        }
      });

      const {
        year_id, libelle, type_year, year, start_at, end_at, adhesion_price, beneficiary_config,
        complementary_config, refund_config, adhesion_type, adhesion_fee_required,
      } = payload;

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'êtes pas autorisé à faire cette action";
        return response.status(401).json(apiResponse);
      }

      const yearToUpdate = await InsuranceYear.query()
        .where('id', year_id)
        .where('insurance_company_id', insurance.id)
        .first();

      if (!yearToUpdate) {
        apiResponse.message = "Année assurantielle introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const currentDate = DateTime.now();
      const currentYear = currentDate.year;
      const nextYear = currentYear + 1;

      // Vérification des règles métier
      if (yearToUpdate.status === YearStatus.Started) {
        apiResponse.message = "Impossible de modifier une année assurantielle active. Les modifications ne sont autorisées que pour les années futures ou en attente d'activation.";
        status = 422;
        return response.status(status).json(apiResponse);
      }

      if (yearToUpdate.year < currentYear || yearToUpdate.status === 'expired') {
        apiResponse.message = "Les années assurantielles terminées ne peuvent pas être modifiées.";
        status = 422;
        return response.status(status).json(apiResponse);
      }

      if (yearToUpdate.year === nextYear && currentDate > DateTime.fromObject({ year: currentYear, month: 12, day: 31 })) {
        apiResponse.message = "Les modifications pour l'année suivante doivent être effectuées avant le 31/12 de l'année en cours.";
        status = 422;
        return response.status(status).json(apiResponse);
      }

      // Validation des dates pour l'année commerciale
      if (type_year === 'commercial') {
        if (!start_at || !end_at) {
          apiResponse.message = "Les dates de début et de fin sont requises pour une année commerciale.";
          status = 400;
          return response.status(status).json(apiResponse);
        }
        if (start_at > end_at) {
          apiResponse.message = "La date de début doit être antérieure à la date de fin.";
          status = 400;
          return response.status(status).json(apiResponse);
        }
      }

      // Vérification des chevauchements
      const overlappingYear = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .whereNot('id', year_id)
        .where((query) => {
          if (end_at) {
            query.where('start_at', '<=', String(end_at.toISODate()));
          } else if (yearToUpdate.endAt) {
            query.where('start_at', '<=', String(yearToUpdate.endAt.toISODate()));
          }
        })
        .where((query) => {
          if (start_at) {
            query.where('end_at', '>=', String(start_at.toISODate())); // Utiliser toISODate() si start_at n'est pas null
          } else if (yearToUpdate.startAt) {
            query.where('end_at', '>=', String(yearToUpdate.startAt.toISODate())); // Fallback sur yearToUpdate.startAt
          }
        })
        .first();

      if (overlappingYear) {
        apiResponse.message = "Une année assurantielle chevauche déjà cette période.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Mise à jour des champs
      yearToUpdate.merge({
        libelle: libelle !== undefined ? libelle : yearToUpdate.libelle,
        typeYear: type_year !== undefined ? type_year : yearToUpdate.typeYear,
        year: year !== undefined ? year : yearToUpdate.year,
        startAt: start_at !== undefined ? start_at : yearToUpdate.startAt,
        endAt: end_at !== undefined ? end_at : yearToUpdate.endAt,
        adhesionPrice: adhesion_price !== undefined ? adhesion_price : yearToUpdate.adhesionPrice,
        adhesionType: adhesion_type !== undefined ? adhesion_type as 'renewable' | 'unique' : yearToUpdate.adhesionType,
        beneficiaryConfig: beneficiary_config !== undefined ? JSON.stringify(beneficiary_config) : yearToUpdate.beneficiaryConfig,
        complementaryConfig: complementary_config !== undefined ? JSON.stringify(complementary_config) : yearToUpdate.complementaryConfig,
        refundConfig: refund_config !== undefined ? JSON.stringify(refund_config) : yearToUpdate.refundConfig,
        adhesionFeeRequired: adhesion_fee_required !== undefined ? adhesion_fee_required : yearToUpdate.adhesionFeeRequired,
      });

      await yearToUpdate.save();

      apiResponse.success = true;
      apiResponse.message = "Année assurantielle mise à jour avec succès";
      apiResponse.result = yearToUpdate;
      status = 200;
      return response.status(status).json(apiResponse);

    } catch (error) {
      console.log("Erreur lors de la mise à jour de l'année assurantielle :", error);
      apiResponse.message = "Échec de la mise à jour de l'année assurantielle";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async savePackageUpdate({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "Échec de l'enregistrement de la mise à jour",
      result: null,
      errors: null,
    };
    let status = 200;

    try {
      // Validation des données
      const payload = await request.validate({
        schema: schema.create({
          package_id: schema.number(),
          insurance_year_id: schema.number(),
          name: schema.string.optional(),
          payment_type: schema.enum.optional(['monthly', 'annual']),
          validity: schema.number.optional(),
          taux: schema.number.optional(),
          plafond: schema.number.optional(),
          price: schema.number.optional(),
          status: schema.enum.optional(['pending', 'updated', 'expired']),
          start_update: schema.date.optional(),
          end_update: schema.date.optional(),
          type: schema.enum.optional(['individual', 'team']),
          visibility: schema.enum.optional(['public', 'private']),
          plafond_config: schema.object.optional().members({
            principal: schema.number(),
            partner: schema.number(),
            child: schema.number()
          }),
          fees_config: schema.object.optional().members({
            principal_fee: schema.number(),
            partner_fee: schema.number(),
            child_fee: schema.number(),
          }),
          tranches_config: schema.object.optional().members({
            nbre_tranches: schema.number(),
            percentages: schema.array().members(schema.number())
          }),
          products: schema.array.optional().members(
            schema.object().members({
              action: schema.enum(['add', 'update', 'remove']),
              product_id: schema.number(),
              quantity: schema.number.optional(),
              public_price: schema.number.optional(),
            })
          ),
          analyzes: schema.array.optional().members(
            schema.object().members({
              action: schema.enum(['add', 'update', 'remove']),
              analyze_id: schema.number(),
              public_price: schema.number.optional(),
            })
          ),
          reason: schema.string.optional(),
        }),
      });

      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }

      const {
        package_id, insurance_year_id, name, payment_type, validity, taux, plafond,
        price, status: updateStatus, start_update, end_update, products, analyzes, reason,
        plafond_config, fees_config, tranches_config, type, visibility,
      } = payload;

      // Vérification de l'année assurantielle
      const insuranceYear = await InsuranceYear.findOrFail(insurance_year_id);
      if (insuranceYear.status !== 'pending') {
        apiResponse.message = "Les modifications ne sont autorisées que pour une année assurantielle programmée ou expirée.";
        status = 422;
        return response.status(status).json(apiResponse);
      }

      // Validation des dates de mise à jour
      if (start_update && end_update && start_update > end_update) {
        apiResponse.message = "La date de début de mise à jour doit être antérieure à la date de fin.";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Sauvegarde de la version actuelle du package
      const currentPackage = await Package.findOrFail(package_id);
      const savedVersion = currentPackage.version || '1.0.0';

      const newVersion = await this.incrementVersion(savedVersion);

      // Check if there's a pending update for this package and insurance year
      const existingUpdate = await PackageUpdate.query()
        .where('package_id', package_id)
        .where('insurance_year_id', insurance_year_id)
        .where('status', 'pending')
        .first();

      let packageUpdate: PackageUpdate;

      if (existingUpdate) {
        // Update existing pending package update
        existingUpdate.merge({
          currentVersion: savedVersion,
          newVersion: newVersion,
          name: name || currentPackage.name,
          paymentType: payment_type as 'monthly' | 'annual' || currentPackage.payment_type,
          validity: validity || currentPackage.validity,
          taux: taux || currentPackage.taux,
          plafond: plafond || currentPackage.plafond,
          price: price || currentPackage.price,
          status: updateStatus as 'pending' | 'updated' | 'expired' || currentPackage.status,
          startUpdate: start_update || existingUpdate.startUpdate,
          endUpdate: end_update || existingUpdate.endUpdate,
          type: type !== undefined ? (type as 'individual' | 'team') : existingUpdate.type,
          visibility: visibility !== undefined ? (visibility as 'public' | 'private') : existingUpdate.visibility,
          plafondConfig: plafond_config ? JSON.stringify(plafond_config) : existingUpdate.plafondConfig,
          feesConfig: fees_config ? JSON.stringify(fees_config) : existingUpdate.feesConfig,
          tranchesConfig: tranches_config ? JSON.stringify(tranches_config) : existingUpdate.tranchesConfig,
          products: products ? JSON.stringify(products) : existingUpdate.products,
          analyzes: analyzes ? JSON.stringify(analyzes) : existingUpdate.analyzes,
          reason: reason || existingUpdate.reason,
          updatedBy: authUser.id
        });
        packageUpdate = await existingUpdate.save();
      } else {
        // Create new package update
        packageUpdate = await PackageUpdate.create({
          packageId: package_id,
          insuranceYearId: insurance_year_id,
          currentVersion: savedVersion,
          newVersion: newVersion,
          name: name || currentPackage.name,
          paymentType: payment_type as 'monthly' | 'annual' || currentPackage.payment_type,
          validity: validity || currentPackage.validity,
          taux: taux || currentPackage.taux,
          plafond: plafond || currentPackage.plafond,
          price: price || currentPackage.price,
          status: updateStatus as 'pending' | 'updated' | 'expired' || currentPackage.status,
          startUpdate: start_update || null,
          endUpdate: end_update || null,
          type: type !== undefined ? (type as 'individual' | 'team') : currentPackage.type,
          visibility: visibility !== undefined ? (visibility as 'public' | 'private') : currentPackage.visibility,
          plafondConfig: plafond_config ? JSON.stringify(plafond_config) : null,
          feesConfig: fees_config ? JSON.stringify(fees_config) : null,
          tranchesConfig: tranches_config ? JSON.stringify(tranches_config) : null,
          products: products ? JSON.stringify(products) : null,
          analyzes: analyzes ? JSON.stringify(analyzes) : null,
          reason: reason || null,
          createdBy: authUser.id,
        });
      }

      if (!packageUpdate) {
        apiResponse.message = "Échec de l'enregistrement de la mise à jour";
        status = 500;
        return response.status(status).json(apiResponse);
      }

      apiResponse.success = true;
      apiResponse.message = "Mise à jour du package enregistrée avec succès";
      apiResponse.result = packageUpdate;
      status = 201;
      return response.status(status).json(apiResponse);

    } catch (error) {
      console.log("Erreur lors de l'enregistrement de la mise à jour :", error);
      apiResponse.message = error.message;
      apiResponse.errors = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async activateInsuranceYearAndUpdatePackages({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          year_id: schema.number(),
        })
      });
      const { year_id } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }

      // Get and activate the pending year
      const yearToUpdate = await InsuranceYear.query()
        .where('id', year_id)
        .where('insurance_company_id', insurance.id)
        .where('status', 'pending')
        .first();

      if (!yearToUpdate) {
        apiResponse.message = "Année assurantielle introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        const lastActiveYear = await InsuranceYear.query()
          .where('insurance_company_id', insurance.id)
          .where('status', YearStatus.Finished)
          .orderBy('created_at', 'desc')
          .first();

        if (lastActiveYear) {
          lastActiveYear.merge({
            status: YearStatus.Expired,
          });
          await lastActiveYear.useTransaction(trx).save();
        }

        //check exist package update for this year
        const packageUpdate = await PackageUpdate.query()
          .where('insurance_year_id', year_id)
          .where('status', 'pending')
          .preload('package')
          .first();
        let product_is_updateed = false;
        let analyze_is_updateed = false;

        if (packageUpdate) {
          const insurancePackage = packageUpdate.package;
          if (insurancePackage) {
            insurancePackage.merge({
              name: packageUpdate.name ?? insurancePackage.name,
              price: packageUpdate.price ?? insurancePackage.price,
              plafond: packageUpdate.plafond ?? insurancePackage.plafond,
              taux: packageUpdate.taux ?? insurancePackage.taux,
              validity: packageUpdate.validity ?? insurancePackage.validity,
              payment_type: packageUpdate.paymentType as 'monthly' | 'annual',
              version: packageUpdate.newVersion,
              type: packageUpdate.type as 'individual' | 'team' || insurancePackage.type,
              visibility: packageUpdate.visibility as 'public' | 'private' || insurancePackage.visibility,
              plafondConfig: packageUpdate.plafondConfig ? JSON.parse(packageUpdate.plafondConfig) : insurancePackage.plafondConfig,
              feesConfig: packageUpdate.feesConfig ? JSON.parse(packageUpdate.feesConfig) : insurancePackage.feesConfig,
              tranchesConfig: packageUpdate.tranchesConfig ? JSON.parse(packageUpdate.tranchesConfig) : insurancePackage.tranchesConfig,
            });
            await insurancePackage.useTransaction(trx).save();

            //updates products and analyzes
            if (packageUpdate.products) {
              const products = JSON.parse(packageUpdate.products) as ProductToUpdate[];

              if (products.length > 0) {
                // Group products by action type for batch processing
                const productsByAction = products.reduce((acc, product) => {
                  if (!acc[product.action]) {
                    acc[product.action] = [];
                  }
                  acc[product.action].push(product);
                  return acc;
                }, {} as Record<string, ProductToUpdate[]>);

                // Handle additions in batch
                if (productsByAction['add']) {
                  const addPayload = productsByAction['add'].reduce((acc, product) => {
                    acc[product.product_id] = {
                      quantity: product.quantity,
                      publicPrice: product.public_price
                    };
                    return acc;
                  }, {});
                  await insurancePackage.related('products').attach(addPayload, trx);
                }

                // Handle updates in batch
                if (productsByAction['update']) {
                  const updatePromises = productsByAction['update'].map(product =>
                    insurancePackage.related('products').query()
                      .where('product_id', product.product_id)
                      .useTransaction(trx)
                      .update({
                        quantity: product.quantity,
                        publicPrice: product.public_price
                      })
                  );
                  await Promise.all(updatePromises);
                }

                // Handle removals in batch
                if (productsByAction['remove']) {
                  const productIdsToRemove = productsByAction['remove'].map(p => p.product_id);
                  await insurancePackage.related('products').query()
                    .whereIn('product_id', productIdsToRemove)
                    .update({ is_active: false }); // Soft delete by setting is_active to false
                }
                product_is_updateed = true;
              }
            }

            if (packageUpdate.analyzes) {
              const analyzes = JSON.parse(packageUpdate.analyzes) as AnalyzeToUpdate[];
              if (analyzes.length > 0) {
                // Group analyzes by action type for batch processing
                const analyzesByAction = analyzes.reduce((acc, analyze) => {
                  if (!acc[analyze.action]) {
                    acc[analyze.action] = [];
                  }
                  acc[analyze.action].push(analyze);
                  return acc;
                });

                // Handle additions in batch
                if (analyzesByAction['add']) {
                  const addPayload = analyzesByAction['add'].reduce((acc, analyze) => {
                    acc[analyze.analyze_id] = {
                      publicPrice: analyze.public_price
                    };
                    return acc;
                  }, {});
                  await insurancePackage.related('analyzes').attach(addPayload, trx);
                }

                // Handle updates in batch
                if (analyzesByAction['update']) {
                  const updatePromises = analyzesByAction['update'].map(analyze =>
                    insurancePackage.related('analyzes').query()
                      .where('analyze_id', analyze.analyze_id)
                      .useTransaction(trx)
                      .update({
                        publicPrice: analyze.public_price
                      })
                  );
                  await Promise.all(updatePromises);
                }

                // Handle removals in batch
                if (analyzesByAction['remove']) {
                  const analyzeIdsToRemove = analyzesByAction['remove'].map(a => a.analyze_id);
                  await insurancePackage.related('analyzes').detach(analyzeIdsToRemove, trx);
                }
                analyze_is_updateed = true;
              }
            }
          }
        }

        yearToUpdate.merge({
          status: YearStatus.Active,
        });
        await yearToUpdate.useTransaction(trx).save();

        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Année assurantielle démarrée avec succès";
        apiResponse.result = {
          year: yearToUpdate,
          product_is_updateed,
          analyze_is_updateed
        };
        status = 200;
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("Erreur lors de la mise à jour de l'année assurantielle :", error);
        apiResponse.message = "Échec de la mise à jour de l'année assurantielle";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in update insurance year status", error);
      apiResponse.message = "Echec de la mise à jour de l'année assurantielle";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async startInsuranceYear({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          year_id: schema.number(),
        })
      });
      const { year_id } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      // Get and activate the active year
      const yearToUpdate = await InsuranceYear.query()
        .where('id', year_id)
        .where('insurance_company_id', insurance.id)
        .orderBy('created_at', 'desc')
        .first();
      if (!yearToUpdate) {
        apiResponse.message = "Année assurantielle introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      if (yearToUpdate.status !== YearStatus.Active) {
        apiResponse.message = "Année assurantielle non activée";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      // Check if there's already a started year
      const startedYear = await InsuranceYear.query()
        .where('insurance_company_id', insurance.id)
        .where('status', YearStatus.Started)
        .first();
      if (startedYear) {
        apiResponse.message = "Une année assurantielle est déjà démarrée";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      //check published package exist
      const publishedPackage = await Package.query()
        .where('insurance_company_id', insurance.id)
        .where('status', PackageStatus.Published)
        .first();
      if (!publishedPackage) {
        apiResponse.message = "Aucun package publié";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      yearToUpdate.merge({
        status: YearStatus.Started,
      });
      await yearToUpdate.save();
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle démarrée avec succès";
      apiResponse.result = yearToUpdate;
      status = 200;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in start insurance year", error);
      apiResponse.message = "Echec de la démarrage de l'année assurantielle";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async finishedInsuranceYear({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          year_id: schema.number(),
        })
      });
      const { year_id } = payload;
      const authUser = await auth.authenticate();
      if (!authUser) {
        apiResponse.message = "Vous n'etes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const insurance = await this.getInsuranceCompanyByUser(authUser.id);
      if (!insurance) {
        apiResponse.message = "Vous n'etes pas gestionnaire de compagnie";
        return response.status(401).json(apiResponse);
      }
      // Get and activate the active year
      const yearToUpdate = await InsuranceYear.query()
        .where('id', year_id)
        .where('insurance_company_id', insurance.id)
        .orderBy('created_at', 'desc')
        .first();
      if (!yearToUpdate) {
        apiResponse.message = "Année assurantielle introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      if (yearToUpdate.status !== YearStatus.Started) {
        apiResponse.message = "Année assurantielle n'est pas démarrée";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      yearToUpdate.merge({
        status: YearStatus.Finished,
        finishedAt: DateTime.now(),
      });
      await yearToUpdate.save();
      apiResponse.success = true;
      apiResponse.message = "Année assurantielle terminée avec succès";
      apiResponse.result = yearToUpdate;
      status = 200;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error in finished insurance year", error);
      apiResponse.message = "Echec de la fin de l'année assurantielle";
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }


}
