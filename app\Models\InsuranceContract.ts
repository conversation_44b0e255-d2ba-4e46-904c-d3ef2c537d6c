import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Team from './Team'
import InsuranceCompany from './InsuranceCompany'
import Package from './Package'
import InsuranceYear from './InsuranceYear'

export default class InsuranceContract extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column({ columnName: 'team_id' })
  public teamId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'package_id' })
  public packageId: number

  @column({ columnName: 'insurance_year_id' })
  public insuranceYearId: number

  @column({ columnName: 'contract_number' })
  public contractNumber: string

  @column.date({ columnName: 'effective_from' })
  public effectiveFrom: DateTime

  @column.date({ columnName: 'effective_to' })
  public effectiveTo: DateTime

  @column({ columnName: 'status' })
  public status: 'draft' | 'validate' | 'active' | 'terminated' | 'renewed'

  @column({ columnName: 'payment_status' })
  public paymentStatus: 'unpaid' | 'partial' | 'paid'

  @column({ columnName: 'annual_premium' })
  public annualPremium: number

  @column({ columnName: 'is_paid' })
  public isPaid: boolean

  @column.date({ columnName: 'payment_date' })
  public paymentDate: DateTime | null

  @column({ columnName: 'payment_reference' })
  public paymentReference: string | null

  @column({ columnName: 'auto_renewal' })
  public autoRenewal: boolean

  @column.date({ columnName: 'renewal_deadline' })
  public renewalDeadline: DateTime | null

  @column({ columnName: 'documents' })
  public documents: any | null

  @column({ columnName: 'created_by' })
  public createdBy: number

  @column.dateTime({ columnName: 'activated_at' })
  public activatedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  public deletedAt: DateTime | null

  @belongsTo(() => Team, {
    foreignKey: 'teamId',
    localKey: 'id',
  })
  public team: BelongsTo<typeof Team>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insuranceCompany: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => Package, {
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public package: BelongsTo<typeof Package>

  @belongsTo(() => InsuranceYear, {
    foreignKey: 'insuranceYearId',
    localKey: 'id',
  })
  public insurance_year: BelongsTo<typeof InsuranceYear>
}
